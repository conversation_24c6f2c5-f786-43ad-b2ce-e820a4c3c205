#!/usr/bin/env python3
"""
Comprehensive scan for ALL orphaned documents in the system.
This will identify and help clean up documents causing query issues.
"""

import json
import os
from pathlib import Path
from collections import defaultdict

def scan_all_orphaned_documents():
    """Scan for all types of orphaned documents"""
    docstore_file = Path("storage/docstore.json")
    
    if not docstore_file.exists():
        print("❌ docstore.json not found")
        return
    
    try:
        with open(docstore_file, 'r') as f:
            docstore_data = json.load(f)
        
        print("=== COMPREHENSIVE ORPHANED DOCUMENT SCAN ===\n")
        
        # Categories of issues
        orphaned_documents = []  # Documents without SharePoint ID
        broken_references = []   # Documents with broken/invalid references
        duplicate_sharepoint_ids = defaultdict(list)  # Multiple docs with same SP ID
        unknown_sources = []     # Documents from unknown sources
        
        all_documents = []
        sharepoint_ids_found = set()
        
        for doc_id, doc_data in docstore_data.get("docstore/data", {}).items():
            if isinstance(doc_data, dict) and "__data__" in doc_data:
                try:
                    # Handle both string and dict formats
                    if isinstance(doc_data["__data__"], str):
                        doc_content = json.loads(doc_data["__data__"])
                    else:
                        doc_content = doc_data["__data__"]
                    
                    metadata = doc_content.get("metadata", {})
                    
                    # Extract document information
                    doc_info = {
                        "doc_id": doc_id,
                        "file_name": metadata.get("file_name", "Unknown"),
                        "source": metadata.get("source", "Unknown"),
                        "sharepoint_id": metadata.get("sharepoint_id"),
                        "file_type": metadata.get("file_type", "Unknown"),
                        "created": metadata.get("created_datetime", "Unknown"),
                        "drive_id": metadata.get("drive_id", "Unknown")
                    }
                    
                    all_documents.append(doc_info)
                    
                    # Check for orphaned documents (no SharePoint ID)
                    if not doc_info["sharepoint_id"]:
                        orphaned_documents.append(doc_info)
                    else:
                        # Track SharePoint IDs for duplicate detection
                        duplicate_sharepoint_ids[doc_info["sharepoint_id"]].append(doc_info)
                        sharepoint_ids_found.add(doc_info["sharepoint_id"])
                    
                    # Check for unknown sources
                    if doc_info["source"] not in ["sharepoint_sync", "manual_upload", "local_file"]:
                        unknown_sources.append(doc_info)
                    
                except Exception as e:
                    broken_references.append({
                        "doc_id": doc_id,
                        "error": str(e),
                        "raw_data_type": type(doc_data.get("__data__")).__name__
                    })
        
        # Identify actual duplicates (more than one document per SharePoint ID)
        actual_duplicates = {sp_id: docs for sp_id, docs in duplicate_sharepoint_ids.items() if len(docs) > 1}
        
        # Print comprehensive report
        print(f"📊 SCAN RESULTS:")
        print(f"   Total documents found: {len(all_documents)}")
        print(f"   Unique SharePoint IDs: {len(sharepoint_ids_found)}")
        print()
        
        # 1. Orphaned documents (no SharePoint ID)
        print(f"🚨 ORPHANED DOCUMENTS ({len(orphaned_documents)}):")
        if orphaned_documents:
            print("   These documents have no SharePoint ID and may be causing issues:")
            for i, doc in enumerate(orphaned_documents, 1):
                print(f"   {i:2d}. {doc['file_name']}")
                print(f"       Doc ID: {doc['doc_id']}")
                print(f"       Source: {doc['source']}")
                print(f"       Created: {doc['created']}")
                print()
        else:
            print("   ✅ No orphaned documents found")
        
        # 2. Broken references
        print(f"💥 BROKEN REFERENCES ({len(broken_references)}):")
        if broken_references:
            print("   These documents have corrupted data:")
            for ref in broken_references:
                print(f"   - Doc ID: {ref['doc_id']}")
                print(f"     Error: {ref['error']}")
                print(f"     Data type: {ref['raw_data_type']}")
                print()
        else:
            print("   ✅ No broken references found")
        
        # 3. Duplicate SharePoint IDs
        print(f"🔄 DUPLICATE SHAREPOINT IDS ({len(actual_duplicates)}):")
        if actual_duplicates:
            print("   Multiple documents share the same SharePoint ID:")
            for sp_id, docs in actual_duplicates.items():
                print(f"   SharePoint ID: {sp_id}")
                for doc in docs:
                    print(f"     - {doc['file_name']} (Doc ID: {doc['doc_id'][:8]}...)")
                print()
        else:
            print("   ✅ No duplicate SharePoint IDs found")
        
        # 4. Unknown sources
        print(f"❓ UNKNOWN SOURCES ({len(unknown_sources)}):")
        if unknown_sources:
            print("   Documents from unexpected sources:")
            for doc in unknown_sources:
                print(f"   - {doc['file_name']} (Source: {doc['source']})")
        else:
            print("   ✅ All documents have known sources")
        
        # 5. Look for specific problematic documents
        print(f"\n🔍 SPECIFIC PROBLEM DETECTION:")
        
        # Check for Optimising document (should be deleted)
        optimising_docs = [doc for doc in all_documents if "OPTIMISING" in doc['file_name'].upper()]
        if optimising_docs:
            print(f"   ⚠️  Found {len(optimising_docs)} OPTIMISING documents (should be deleted):")
            for doc in optimising_docs:
                print(f"     - {doc['file_name']} (Doc ID: {doc['doc_id'][:8]}...)")
                print(f"       SharePoint ID: {doc['sharepoint_id']}")
                print(f"       Source: {doc['source']}")
        else:
            print("   ✅ No Optimising documents found")
        
        # Check for Evolution document (should be deleted)
        evolution_docs = [doc for doc in all_documents if "EVOLUTION" in doc['file_name'].upper()]
        if evolution_docs:
            print(f"   ⚠️  Found {len(evolution_docs)} EVOLUTION documents (should be deleted):")
            for doc in evolution_docs:
                print(f"     - {doc['file_name']} (Doc ID: {doc['doc_id'][:8]}...)")
                print(f"       SharePoint ID: {doc['sharepoint_id']}")
        else:
            print("   ✅ No Evolution documents found")
        
        print(f"\n💡 RECOMMENDATIONS:")
        total_problematic = len(orphaned_documents) + len(broken_references) + len(optimising_docs) + len(evolution_docs)
        
        if total_problematic > 0:
            print(f"   🔧 Found {total_problematic} problematic documents that need cleanup")
            print(f"   📋 Priority cleanup order:")
            print(f"      1. Remove Optimising documents ({len(optimising_docs)} found)")
            print(f"      2. Remove Evolution documents ({len(evolution_docs)} found)")
            print(f"      3. Clean up orphaned documents ({len(orphaned_documents)} found)")
            print(f"      4. Fix broken references ({len(broken_references)} found)")
        else:
            print(f"   ✅ No problematic documents found - index appears clean")
        
        return {
            "orphaned": orphaned_documents,
            "broken": broken_references,
            "duplicates": actual_duplicates,
            "optimising": optimising_docs,
            "evolution": evolution_docs,
            "unknown_sources": unknown_sources
        }
        
    except Exception as e:
        print(f"❌ Error scanning documents: {e}")
        return None

if __name__ == "__main__":
    results = scan_all_orphaned_documents()
    if results:
        print(f"\n🎯 CLEANUP SCRIPT READY")
        print(f"   Use the returned data to create targeted cleanup scripts")