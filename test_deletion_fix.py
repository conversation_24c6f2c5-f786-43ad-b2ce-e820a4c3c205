#!/usr/bin/env python3
"""
Test script to verify the deletion fix for the Evolution document.
This script will directly check the index and test the deletion functionality.
"""

import os
import sys
import asyncio
import json
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from app import app, delete_document_with_verification
from config import Settings

async def test_deletion_fix():
    """Test the deletion fix for the Evolution document."""
    print("=== TESTING DELETION FIX FOR EVOLUTION DOCUMENT ===\n")
    
    # Initialize settings
    settings = Settings()
    
    # Check if we have access to the index
    if not hasattr(app.state, 'index') or not app.state.index:
        print("❌ Index not available - application may not be initialized")
        return
    
    # Check current state of Evolution documents
    evolution_docs = []
    if hasattr(app.state.index, "docstore") and hasattr(app.state.index.docstore, "docs"):
        for doc_id, doc_info in app.state.index.docstore.docs.items():
            # Check if this is an Evolution document
            file_name = doc_info.metadata.get("file_name", "") if doc_info.metadata else ""
            if "EVOLUTION" in file_name.upper():
                evolution_docs.append({
                    "doc_id": doc_id,
                    "file_name": file_name,
                    "sharepoint_id": doc_info.metadata.get("sharepoint_id") if doc_info.metadata else None,
                })
    
    print(f"Found {len(evolution_docs)} Evolution documents in the index:")
    
    if not evolution_docs:
        print("✅ No Evolution documents found - deletion appears to be working!")
        return
    
    # Test deletion for each Evolution document
    for i, doc in enumerate(evolution_docs, 1):
        print(f"\n{i}. Testing deletion for Evolution document:")
        print(f"   - Doc ID: {doc['doc_id']}")
        print(f"   - File Name: {doc['file_name']}")
        print(f"   - SharePoint ID: {doc['sharepoint_id']}")
        
        # Try to delete the document
        print(f"   - Attempting deletion...")
        deletion_result = await delete_document_with_verification(doc['doc_id'])
        
        if deletion_result:
            print(f"   ✅ Deletion successful!")
        else:
            print(f"   ❌ Deletion failed!")
    
    # Check the state after deletion attempts
    print(f"\n=== POST-DELETION STATE ===")
    
    remaining_evolution_docs = []
    total_docs = 0
    if hasattr(app.state.index, "docstore") and hasattr(app.state.index.docstore, "docs"):
        for doc_id, doc_info in app.state.index.docstore.docs.items():
            total_docs += 1
            file_name = doc_info.metadata.get("file_name", "") if doc_info.metadata else ""
            if "EVOLUTION" in file_name.upper():
                remaining_evolution_docs.append({
                    "doc_id": doc_id,
                    "file_name": file_name,
                })
    
    print(f"Total documents in index: {total_docs}")
    print(f"Remaining Evolution documents: {len(remaining_evolution_docs)}")
    
    if remaining_evolution_docs:
        print("\nRemaining Evolution documents:")
        for doc in remaining_evolution_docs:
            print(f"  - {doc['file_name']} (doc_id: {doc['doc_id'][:8]}...)")
        print("❌ Evolution documents still present - deletion fix needs more work")
    else:
        print("✅ All Evolution documents successfully deleted!")
    
    print("\n=== TEST COMPLETE ===")

if __name__ == "__main__":
    print("To run this test, execute it in the context of the running application:")
    print("python test_deletion_fix.py")
    print("\nThe test will only work if the application is already running and initialized.")