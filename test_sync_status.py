import requests
import json
from datetime import datetime

# App base URL
BASE_URL = "http://localhost:8082"

# Basic auth credentials from .env
USERNAME = "admin"
PASSWORD = "your-password"

def check_sync_status():
    """Check the sync status and document counts."""
    
    print("=" * 80)
    print("CHECKING DOCUMENT SYNC STATUS")
    print("=" * 80)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # First, get the indexed documents
    try:
        response = requests.get(
            f"{BASE_URL}/api/documents",
            auth=(USERNAME, PASSWORD),
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            indexed_count = data.get('total_documents', 0)
            documents = data.get('documents', [])
            sync_info = data.get('sync_info', {})
            
            print(f"\n📊 INDEXED DOCUMENTS:")
            print(f"   Total: {indexed_count}")
            
            # Count documents by source
            sharepoint_docs = sum(1 for doc in documents if doc.get('sharepoint_id'))
            local_docs = indexed_count - sharepoint_docs
            
            print(f"   From SharePoint: {sharepoint_docs}")
            print(f"   Local uploads: {local_docs}")
            
            # Show sync info
            print(f"\n🔄 SYNC INFORMATION:")
            print(f"   Auto-sync enabled: {sync_info.get('auto_sync_enabled', False)}")
            print(f"   Sync interval: {sync_info.get('sync_interval_minutes', 'N/A')} minutes")
            print(f"   Last sync: {sync_info.get('last_sync_time', 'Never')}")
            print(f"   SharePoint configured: {sync_info.get('sharepoint_configured', False)}")
            
            # Group documents by folder if they have folder info
            folders = {}
            for doc in documents:
                folder = doc.get('folder_path', 'Unknown')
                if folder not in folders:
                    folders[folder] = 0
                folders[folder] += 1
            
            if len(folders) > 1 or 'Unknown' not in folders:
                print(f"\n📁 DOCUMENTS BY FOLDER:")
                for folder, count in sorted(folders.items()):
                    print(f"   {folder}: {count} files")
            
        else:
            print(f"❌ Error getting documents: HTTP {response.status_code}")
            print(response.text)
    
    except Exception as e:
        print(f"❌ Error connecting to app: {e}")
    
    # Try to trigger a manual sync to get SharePoint counts
    print("\n" + "=" * 80)
    print("ATTEMPTING MANUAL SYNC TO GET SHAREPOINT FILE COUNT...")
    print("=" * 80)
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/sync/manual",
            auth=(USERNAME, PASSWORD),
            timeout=120  # Longer timeout for sync
        )
        
        if response.status_code == 200:
            sync_data = response.json()
            
            if sync_data.get('status') == 'sync_completed':
                print(f"\n✅ SYNC COMPLETED:")
                print(f"   SharePoint files found: {sync_data.get('sharepoint_files_found', 0)}")
                print(f"   Files imported: {sync_data.get('files_imported', 0)}")
                print(f"   Files deleted: {sync_data.get('total_deleted', 0)}")
                print(f"   Target folder: {sync_data.get('target_folder', 'Unknown')}")
                
                # Show any errors
                import_errors = sync_data.get('import_errors', [])
                if import_errors:
                    print(f"\n⚠️  Import errors: {len(import_errors)}")
                    for error in import_errors[:5]:  # Show first 5 errors
                        print(f"   - {error}")
                
            elif sync_data.get('status') == 'error':
                print(f"\n❌ Sync error: {sync_data.get('message', 'Unknown error')}")
                if 'diagnostic_info' in sync_data:
                    print("\nDiagnostic info:")
                    print(json.dumps(sync_data['diagnostic_info'], indent=2))
            else:
                print(f"\nSync response: {json.dumps(sync_data, indent=2)}")
                
        else:
            print(f"❌ Error triggering sync: HTTP {response.status_code}")
            try:
                print(response.json())
            except:
                print(response.text)
    
    except Exception as e:
        print(f"❌ Error during sync: {e}")
    
    print("\n" + "=" * 80)
    print("SUMMARY:")
    print("If the SharePoint file count doesn't match the indexed document count,")
    print("it means some files haven't been imported or need to be synced.")
    print("=" * 80)

if __name__ == "__main__":
    check_sync_status()