#!/usr/bin/env python3
"""
Analyze what documents are currently indexed and their sources.
"""

import json
import os
from pathlib import Path
from collections import defaultdict

def analyze_indexed_documents():
    """Analyze all documents currently in the index"""
    docstore_file = Path("storage/docstore.json")
    
    if not docstore_file.exists():
        print("❌ docstore.json not found")
        return
    
    try:
        with open(docstore_file, 'r') as f:
            docstore_data = json.load(f)
        
        print("=== DOCUMENT ANALYSIS ===\n")
        
        documents = []
        sources = defaultdict(int)
        drives = defaultdict(int)
        folders = defaultdict(int)
        
        for doc_id, doc_data in docstore_data.get("docstore/data", {}).items():
            if isinstance(doc_data, dict) and "__data__" in doc_data:
                try:
                    doc_content = json.loads(doc_data["__data__"])
                    metadata = doc_content.get("metadata", {})
                    
                    # Extract key information
                    file_name = metadata.get("file_name", "Unknown")
                    source = metadata.get("source", "Unknown")
                    drive_id = metadata.get("drive_id", "Unknown")
                    sharepoint_id = metadata.get("sharepoint_id")
                    web_url = metadata.get("web_url", "")
                    
                    # Try to extract folder path from web_url or file_path
                    folder_path = "Unknown"
                    if web_url and "drives/" in web_url:
                        # Extract folder info from SharePoint URL if possible
                        url_parts = web_url.split("/")
                        if len(url_parts) > 6:
                            folder_path = "SharePoint Drive"
                    
                    file_path = metadata.get("file_path", "")
                    if "temp/" in file_path:
                        # This indicates it was downloaded from SharePoint
                        folder_path = "SharePoint Sync"
                    
                    documents.append({
                        "file_name": file_name,
                        "source": source,
                        "drive_id": drive_id[:20] + "..." if len(drive_id) > 20 else drive_id,
                        "sharepoint_id": sharepoint_id[:15] + "..." if sharepoint_id and len(sharepoint_id) > 15 else sharepoint_id,
                        "folder_path": folder_path,
                        "doc_id": doc_id[:8] + "..."
                    })
                    
                    sources[source] += 1
                    drives[drive_id] += 1
                    
                except Exception as e:
                    print(f"   ⚠️  Error parsing document {doc_id}: {e}")
        
        print(f"📊 Total documents found: {len(documents)}")
        print(f"\n📂 Documents by source:")
        for source, count in sources.items():
            print(f"   {source}: {count} documents")
        
        print(f"\n💿 Documents by drive:")
        for drive_id, count in drives.items():
            display_drive = drive_id[:30] + "..." if len(drive_id) > 30 else drive_id
            print(f"   {display_drive}: {count} documents")
        
        # Show sample documents
        print(f"\n📋 Sample documents (first 20):")
        print("   File Name | Source | SharePoint ID")
        print("   " + "-" * 80)
        
        for i, doc in enumerate(documents[:20], 1):
            file_display = doc['file_name'][:40] + "..." if len(doc['file_name']) > 40 else doc['file_name']
            sp_id_display = doc['sharepoint_id'] if doc['sharepoint_id'] else "None"
            print(f"   {i:2d}. {file_display:<45} | {doc['source']:<15} | {sp_id_display}")
        
        if len(documents) > 20:
            print(f"   ... and {len(documents) - 20} more documents")
        
        # Look for documents that might be from wrong locations
        print(f"\n🔍 Potential issues:")
        
        no_sharepoint_id = [doc for doc in documents if not doc['sharepoint_id']]
        if no_sharepoint_id:
            print(f"   - {len(no_sharepoint_id)} documents without SharePoint ID (orphaned)")
        
        non_sync_sources = [doc for doc in documents if doc['source'] != 'sharepoint_sync']
        if non_sync_sources:
            print(f"   - {len(non_sync_sources)} documents from non-SharePoint sources")
            for doc in non_sync_sources[:5]:
                print(f"     • {doc['file_name']} (source: {doc['source']})")
        
        # Check for expected documents
        print(f"\n✅ Looking for expected documents:")
        casting_docs = [doc for doc in documents if "CASTING" in doc['file_name'].upper()]
        optimising_docs = [doc for doc in documents if "OPTIMISING" in doc['file_name'].upper()]
        evolution_docs = [doc for doc in documents if "EVOLUTION" in doc['file_name'].upper()]
        
        print(f"   - CASTING documents: {len(casting_docs)}")
        print(f"   - OPTIMISING documents: {len(optimising_docs)}")
        print(f"   - EVOLUTION documents: {len(evolution_docs)} (should be 0)")
        
        if evolution_docs:
            print("   ⚠️  Evolution documents still found:")
            for doc in evolution_docs:
                print(f"     • {doc['file_name']} (doc_id: {doc['doc_id']})")
        
    except Exception as e:
        print(f"❌ Error analyzing documents: {e}")

if __name__ == "__main__":
    analyze_indexed_documents()