import asyncio
import os
import sys
from dotenv import load_dotenv
from sharepoint_client import SharePointClient
from datetime import datetime
import json

load_dotenv()

async def find_handbook_in_sharepoint():
    """Search for the DDB Employee Handbook.pdf in SharePoint to find its current location."""
    
    print("=" * 80)
    print("SEARCHING FOR DDB EMPLOYEE HANDBOOK.PDF IN SHAREPOINT")
    print("=" * 80)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # SharePoint ID we're looking for
    target_sharepoint_id = "01ADQ5T2BBJ4DG5CFI2VD2ERRBHFYVFOSN"
    print(f"Looking for SharePoint ID: {target_sharepoint_id}")
    
    # Initialize SharePoint client
    client = SharePointClient()
    drive_id = "b!8qRbvGPCU0vmvPqIMdU1CVj1Kch1aZKsPoMqmcF7S3A5GP8ezewSrgF4XXBsT-a"
    
    # Get token
    token = None
    if len(sys.argv) > 1:
        token = sys.argv[1]
        print("✓ Using token from command line")
    else:
        # Try cached token
        token_cache_path = "storage/data/token_caches/admin_token_cache.json"
        try:
            if os.path.exists(token_cache_path):
                with open(token_cache_path, 'r') as f:
                    cache_data = json.load(f)
                    token = cache_data.get('access_token')
                    if token:
                        print("✓ Using cached admin token")
        except Exception as e:
            print(f"❌ Error reading token: {e}")
    
    if not token:
        print("❌ No access token available")
        print("Please provide token as argument or login to the app first")
        return
    
    print("\n" + "=" * 80)
    print("SEARCHING ALL LOCATIONS...")
    print("=" * 80)
    
    found_locations = []
    
    async def search_folder(folder_path, level=0):
        """Recursively search for the handbook file."""
        indent = "  " * level
        display_path = folder_path if folder_path else "(root)"
        
        try:
            # Get items in folder
            items = await client.list_files(
                drive_id=drive_id,
                token=token,
                folder_path=folder_path if folder_path else None
            )
            
            print(f"{indent}📁 Searching: {display_path}")
            
            # Check each item
            for item in items:
                item_name = item.get('name', '')
                item_id = item.get('id', '')
                
                # Check if this is our handbook
                if item_id == target_sharepoint_id:
                    print(f"{indent}  ✅ FOUND: {item_name}")
                    location = {
                        'path': folder_path if folder_path else 'root',
                        'name': item_name,
                        'id': item_id,
                        'type': 'file' if item.get('file') else 'folder',
                        'size': item.get('size', 0),
                        'modified': item.get('lastModifiedDateTime', 'Unknown')
                    }
                    found_locations.append(location)
                elif 'handbook' in item_name.lower():
                    print(f"{indent}  📄 Similar file: {item_name} (ID: {item_id[:20]}...)")
                
                # If it's a folder, search recursively
                if item.get('folder'):
                    subfolder_path = f"{folder_path}/{item_name}" if folder_path else item_name
                    await search_folder(subfolder_path, level + 1)
                    
        except Exception as e:
            print(f"{indent}  ❌ Error searching {display_path}: {e}")
    
    # Search from root
    await search_folder("")
    
    # Display results
    print("\n" + "=" * 80)
    print("SEARCH RESULTS:")
    print("=" * 80)
    
    if found_locations:
        print(f"\n✅ FOUND THE HANDBOOK IN {len(found_locations)} LOCATION(S):")
        for loc in found_locations:
            print(f"\n📍 Location: {loc['path']}")
            print(f"   File name: {loc['name']}")
            print(f"   Size: {loc['size'] / (1024*1024):.2f} MB")
            print(f"   Modified: {loc['modified']}")
            print(f"   SharePoint ID: {loc['id']}")
    else:
        print("\n❌ HANDBOOK NOT FOUND!")
        print("The file may have been:")
        print("1. Deleted from SharePoint")
        print("2. Moved to a location the app doesn't have access to")
        print("3. In a different drive than expected")
    
    print("\n" + "=" * 80)
    print("RECOMMENDATIONS:")
    print("=" * 80)
    print("1. The indexed handbook no longer exists in DDB Group Repository")
    print("2. Clear the index: python clear_index.py")
    print("3. Re-sync to get only current files from DDB Group Repository")
    print("4. If you need the handbook, check where it was found above")
    print("=" * 80)

if __name__ == "__main__":
    asyncio.run(find_handbook_in_sharepoint())