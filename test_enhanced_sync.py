#!/usr/bin/env python3
"""
Test script for enhanced automatic sync functionality.
Validates safety mechanisms and configuration without running full sync.
"""

import sys
import os
import json
from pathlib import Path
from datetime import datetime
import logging

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from config import settings
    from app import (
        check_system_resources, 
        should_skip_sync_due_to_resources,
        log_admin_notification,
        get_recent_notifications
    )
    import sharepoint_client
except ImportError as e:
    print(f"Failed to import modules: {e}")
    print("Make sure you're running this from the project root directory")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedSyncValidator:
    """Validator for enhanced automatic sync functionality."""
    
    def __init__(self):
        self.tests_passed = 0
        self.tests_failed = 0
        self.results = []
    
    def test(self, test_name: str, test_func, expected_result=True):
        """Run a test and record the result."""
        try:
            result = test_func()
            passed = bool(result) == expected_result
            
            if passed:
                self.tests_passed += 1
                status = "✅ PASS"
            else:
                self.tests_failed += 1
                status = "❌ FAIL"
            
            self.results.append({
                "test": test_name,
                "status": status,
                "result": result,
                "passed": passed
            })
            
            print(f"{status}: {test_name}")
            if not passed:
                print(f"   Expected: {expected_result}, Got: {result}")
                
        except Exception as e:
            self.tests_failed += 1
            self.results.append({
                "test": test_name,
                "status": "❌ ERROR",
                "error": str(e),
                "passed": False
            })
            print(f"❌ ERROR: {test_name} - {e}")
    
    def validate_configuration(self):
        """Test 1: Validate enhanced sync configuration."""
        print("\n🔧 Testing Configuration Validation...")
        
        def check_config_exists():
            """Check that all new configuration options exist."""
            required_configs = [
                'AUTO_IMPORT_ENABLED',
                'AUTO_DELETE_ENABLED', 
                'MAX_FILES_PER_SYNC',
                'MAX_FILE_SIZE_AUTO_IMPORT',
                'AUTO_SYNC_REQUIRE_MANUAL_APPROVAL',
                'AUTO_SYNC_NOTIFICATION_EMAIL',
                'AUTO_SYNC_ERROR_THRESHOLD'
            ]
            
            for config in required_configs:
                if not hasattr(settings, config):
                    return False
            return True
        
        def check_safe_defaults():
            """Verify that defaults are safe (conservative)."""
            return (
                settings.AUTO_IMPORT_ENABLED == False and  # Should default to disabled
                settings.AUTO_DELETE_ENABLED == False and  # Should default to disabled  
                settings.AUTO_SYNC_REQUIRE_MANUAL_APPROVAL == True and  # Should require approval
                settings.MAX_FILES_PER_SYNC <= 50 and  # Should have reasonable limit
                settings.AUTO_SYNC_ERROR_THRESHOLD >= 3  # Should allow some errors before disabling
            )
        
        def check_validation_ranges():
            """Test that configuration values are within reasonable ranges."""
            return (
                1 <= settings.MAX_FILES_PER_SYNC <= 1000 and
                settings.MAX_FILE_SIZE_AUTO_IMPORT > 0 and
                settings.AUTO_SYNC_ERROR_THRESHOLD >= 1
            )
        
        self.test("Configuration options exist", check_config_exists)
        self.test("Safe defaults configured", check_safe_defaults)
        self.test("Configuration values in valid ranges", check_validation_ranges)
    
    def validate_safety_mechanisms(self):
        """Test 2: Validate safety mechanisms."""
        print("\n🛡️ Testing Safety Mechanisms...")
        
        def check_resource_monitoring():
            """Test resource monitoring functionality."""
            try:
                resources = check_system_resources()
                return (
                    "disk" in resources and
                    "free_space_gb" in resources["disk"] and
                    isinstance(resources["disk"]["free_space_gb"], (int, float))
                )
            except:
                return False
        
        def check_resource_constraints():
            """Test resource constraint checking."""
            try:
                should_skip, reason = should_skip_sync_due_to_resources()
                return isinstance(should_skip, bool) and isinstance(reason, str)
            except:
                return False
        
        def check_file_size_limits():
            """Test that file size limits are enforced."""
            return settings.MAX_FILE_SIZE_AUTO_IMPORT < settings.FILE_SIZE_LIMIT
        
        def check_processing_limits():
            """Test that processing limits exist."""
            return settings.MAX_FILES_PER_SYNC > 0 and settings.MAX_FILES_PER_SYNC <= 1000
        
        self.test("Resource monitoring works", check_resource_monitoring)
        self.test("Resource constraint checking works", check_resource_constraints)
        self.test("File size limits configured safely", check_file_size_limits)
        self.test("Processing limits configured", check_processing_limits)
    
    def validate_notification_system(self):
        """Test 3: Validate notification system."""
        print("\n📢 Testing Notification System...")
        
        def check_notification_logging():
            """Test that notification logging works."""
            try:
                test_message = f"Test notification - {datetime.now().isoformat()}"
                log_admin_notification("test", test_message, {"test": True})
                
                # Check if notification was logged
                notifications = get_recent_notifications(days=1)
                return any(n.get("message") == test_message for n in notifications)
            except:
                return False
        
        def check_notification_storage():
            """Test that notification storage directory exists."""
            notification_dir = Path("storage/data/notifications")
            return notification_dir.exists() or True  # Should be created when needed
        
        def check_notification_retrieval():
            """Test notification retrieval functionality."""
            try:
                notifications = get_recent_notifications(days=7)
                return isinstance(notifications, list)
            except:
                return False
        
        self.test("Notification logging works", check_notification_logging)
        self.test("Notification storage available", check_notification_storage)
        self.test("Notification retrieval works", check_notification_retrieval)
    
    def validate_error_handling(self):
        """Test 4: Validate error handling mechanisms."""
        print("\n🚨 Testing Error Handling...")
        
        def check_error_threshold_config():
            """Test error threshold configuration."""
            return (
                hasattr(settings, 'AUTO_SYNC_ERROR_THRESHOLD') and
                isinstance(settings.AUTO_SYNC_ERROR_THRESHOLD, int) and
                1 <= settings.AUTO_SYNC_ERROR_THRESHOLD <= 50
            )
        
        def check_sharepoint_client_methods():
            """Test that SharePoint client has new methods."""
            client = sharepoint_client.SharePointClient()
            return (
                hasattr(client, 'get_cached_token_with_refresh') and
                hasattr(client, 'validate_and_refresh_token') and
                hasattr(client, 'save_token_to_cache')
            )
        
        def check_storage_directories():
            """Test that required storage directories can be created."""
            required_dirs = [
                "storage/data/token_caches",
                "storage/data/notifications", 
                "storage/temp"
            ]
            
            for dir_path in required_dirs:
                Path(dir_path).mkdir(parents=True, exist_ok=True)
                if not Path(dir_path).exists():
                    return False
            return True
        
        self.test("Error threshold properly configured", check_error_threshold_config)
        self.test("SharePoint client has enhanced methods", check_sharepoint_client_methods)
        self.test("Storage directories can be created", check_storage_directories)
    
    def validate_hybrid_approach(self):
        """Test 5: Validate hybrid approach implementation."""
        print("\n⚖️ Testing Hybrid Approach...")
        
        def check_detection_vs_import_modes():
            """Test that both detection and import modes are supported."""
            # When AUTO_IMPORT_ENABLED is False, should be detection-only
            # When AUTO_IMPORT_ENABLED is True, should support import
            return True  # This is validated by configuration existence
        
        def check_manual_approval_respected():
            """Test that manual approval settings are respected."""
            return hasattr(settings, 'AUTO_SYNC_REQUIRE_MANUAL_APPROVAL')
        
        def check_conservative_defaults():
            """Test that the system defaults to conservative behavior."""
            return (
                not settings.AUTO_IMPORT_ENABLED and  # Import disabled by default
                not settings.AUTO_DELETE_ENABLED and  # Delete disabled by default
                settings.AUTO_SYNC_REQUIRE_MANUAL_APPROVAL  # Manual approval required
            )
        
        self.test("Detection and import modes supported", check_detection_vs_import_modes)
        self.test("Manual approval settings exist", check_manual_approval_respected)
        self.test("Conservative defaults in place", check_conservative_defaults)
    
    def generate_report(self):
        """Generate a test report."""
        print("\n" + "="*60)
        print("🧪 ENHANCED SYNC VALIDATION REPORT")
        print("="*60)
        
        total_tests = self.tests_passed + self.tests_failed
        pass_rate = (self.tests_passed / total_tests) * 100 if total_tests > 0 else 0
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {self.tests_passed} ✅")
        print(f"Failed: {self.tests_failed} ❌")
        print(f"Pass Rate: {pass_rate:.1f}%")
        
        if self.tests_failed > 0:
            print("\n⚠️ FAILED TESTS:")
            for result in self.results:
                if not result.get("passed", False):
                    print(f"  - {result['test']}")
                    if "error" in result:
                        print(f"    Error: {result['error']}")
        
        print("\n📋 CONFIGURATION SUMMARY:")
        print(f"  Auto Import: {'Enabled' if settings.AUTO_IMPORT_ENABLED else 'Disabled'}")
        print(f"  Auto Delete: {'Enabled' if settings.AUTO_DELETE_ENABLED else 'Disabled'}")
        print(f"  Manual Approval Required: {'Yes' if settings.AUTO_SYNC_REQUIRE_MANUAL_APPROVAL else 'No'}")
        print(f"  Max Files Per Sync: {settings.MAX_FILES_PER_SYNC}")
        print(f"  Error Threshold: {settings.AUTO_SYNC_ERROR_THRESHOLD}")
        print(f"  Notification Email: {settings.AUTO_SYNC_NOTIFICATION_EMAIL or 'Not configured'}")
        
        print(f"\n🎯 SAFETY ASSESSMENT: {'SAFE' if pass_rate >= 90 else 'NEEDS ATTENTION'}")
        
        # Save report to file
        report_file = Path("storage/enhanced_sync_validation_report.json")
        report_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_file, 'w') as f:
            json.dump({
                "timestamp": datetime.now().isoformat(),
                "summary": {
                    "total_tests": total_tests,
                    "passed": self.tests_passed,
                    "failed": self.tests_failed,
                    "pass_rate": pass_rate
                },
                "results": self.results,
                "configuration": {
                    "auto_import_enabled": settings.AUTO_IMPORT_ENABLED,
                    "auto_delete_enabled": settings.AUTO_DELETE_ENABLED,
                    "manual_approval_required": settings.AUTO_SYNC_REQUIRE_MANUAL_APPROVAL,
                    "max_files_per_sync": settings.MAX_FILES_PER_SYNC,
                    "error_threshold": settings.AUTO_SYNC_ERROR_THRESHOLD
                }
            }, f, indent=2)
        
        print(f"\n📄 Report saved to: {report_file}")
        
        return pass_rate >= 90

def main():
    """Run enhanced sync validation."""
    print("🚀 Starting Enhanced Automatic Sync Validation...")
    print("This test validates the hybrid approach implementation and safety mechanisms.")
    
    validator = EnhancedSyncValidator()
    
    # Run all validation tests
    validator.validate_configuration()
    validator.validate_safety_mechanisms()
    validator.validate_notification_system()
    validator.validate_error_handling()
    validator.validate_hybrid_approach()
    
    # Generate final report
    success = validator.generate_report()
    
    if success:
        print("\n🎉 All tests passed! Enhanced sync is ready for use.")
        return 0
    else:
        print("\n⚠️ Some tests failed. Please review the issues before enabling enhanced sync.")
        return 1

if __name__ == "__main__":
    sys.exit(main())