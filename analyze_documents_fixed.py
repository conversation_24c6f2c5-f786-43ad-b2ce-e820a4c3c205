#!/usr/bin/env python3
"""
Analyze what documents are currently indexed and their sources.
Fixed version that handles the correct docstore structure.
"""

import json
import os
from pathlib import Path
from collections import defaultdict

def analyze_indexed_documents():
    """Analyze all documents currently in the index"""
    docstore_file = Path("storage/docstore.json")
    
    if not docstore_file.exists():
        print("❌ docstore.json not found")
        return
    
    try:
        with open(docstore_file, 'r') as f:
            docstore_data = json.load(f)
        
        print("=== DOCUMENT ANALYSIS ===\n")
        
        documents = []
        sources = defaultdict(int)
        drives = defaultdict(int)
        file_types = defaultdict(int)
        sharepoint_ids = set()
        
        for doc_id, doc_data in docstore_data.get("docstore/data", {}).items():
            if isinstance(doc_data, dict) and "__data__" in doc_data:
                try:
                    # Handle both string and dict formats
                    if isinstance(doc_data["__data__"], str):
                        doc_content = json.loads(doc_data["__data__"])
                    else:
                        doc_content = doc_data["__data__"]
                    
                    metadata = doc_content.get("metadata", {})
                    
                    # Extract key information
                    file_name = metadata.get("file_name", "Unknown")
                    source = metadata.get("source", "Unknown")
                    drive_id = metadata.get("drive_id", "Unknown")
                    sharepoint_id = metadata.get("sharepoint_id")
                    web_url = metadata.get("web_url", "")
                    file_type = metadata.get("file_type", "Unknown")
                    
                    documents.append({
                        "file_name": file_name,
                        "source": source,
                        "drive_id": drive_id[:20] + "..." if len(drive_id) > 20 else drive_id,
                        "sharepoint_id": sharepoint_id[:15] + "..." if sharepoint_id and len(sharepoint_id) > 15 else sharepoint_id,
                        "file_type": file_type,
                        "doc_id": doc_id[:8] + "..."
                    })
                    
                    sources[source] += 1
                    drives[drive_id] += 1
                    file_types[file_type] += 1
                    
                    if sharepoint_id:
                        sharepoint_ids.add(sharepoint_id)
                    
                except Exception as e:
                    print(f"   ⚠️  Error parsing document {doc_id}: {e}")
        
        print(f"📊 Total documents found: {len(documents)}")
        print(f"📊 Unique SharePoint documents: {len(sharepoint_ids)}")
        
        print(f"\n📂 Documents by source:")
        for source, count in sources.items():
            print(f"   {source}: {count} documents")
        
        print(f"\n📄 Documents by file type:")
        for file_type, count in file_types.items():
            print(f"   {file_type}: {count} documents")
        
        print(f"\n💿 Documents by drive:")
        for drive_id, count in drives.items():
            display_drive = drive_id[:50] + "..." if len(drive_id) > 50 else drive_id
            print(f"   {display_drive}: {count} documents")
        
        # Show sample documents
        print(f"\n📋 All documents:")
        print("   File Name | Source | SharePoint ID")
        print("   " + "-" * 100)
        
        # Group by unique SharePoint ID to see actual files
        unique_files = {}
        for doc in documents:
            sp_id = doc['sharepoint_id']
            if sp_id and sp_id != "None":
                if sp_id not in unique_files:
                    unique_files[sp_id] = doc
        
        print(f"\n📋 Unique SharePoint files ({len(unique_files)}):")
        for i, (sp_id, doc) in enumerate(unique_files.items(), 1):
            file_display = doc['file_name'][:60] + "..." if len(doc['file_name']) > 60 else doc['file_name']
            print(f"   {i:2d}. {file_display:<65} | {doc['source']:<15} | {sp_id}")
        
        # Look for documents that might be from wrong locations
        print(f"\n🔍 Potential issues:")
        
        no_sharepoint_id = [doc for doc in documents if not doc['sharepoint_id'] or doc['sharepoint_id'] == "None"]
        if no_sharepoint_id:
            print(f"   - {len(no_sharepoint_id)} documents without SharePoint ID (orphaned)")
        
        non_sync_sources = [doc for doc in documents if doc['source'] != 'sharepoint_sync']
        if non_sync_sources:
            print(f"   - {len(non_sync_sources)} documents from non-SharePoint sources")
            for doc in non_sync_sources[:5]:
                print(f"     • {doc['file_name']} (source: {doc['source']})")
        
        # Check for expected documents
        print(f"\n✅ Looking for expected documents:")
        casting_docs = [doc for doc in unique_files.values() if "CASTING" in doc['file_name'].upper()]
        optimising_docs = [doc for doc in unique_files.values() if "OPTIMISING" in doc['file_name'].upper()]
        evolution_docs = [doc for doc in unique_files.values() if "EVOLUTION" in doc['file_name'].upper()]
        
        print(f"   - CASTING documents: {len(casting_docs)}")
        print(f"   - OPTIMISING documents: {len(optimising_docs)}")
        print(f"   - EVOLUTION documents: {len(evolution_docs)} (should be 0)")
        
        if evolution_docs:
            print("   ⚠️  Evolution documents still found:")
            for doc in evolution_docs:
                print(f"     • {doc['file_name']} (doc_id: {doc['doc_id']})")
        
        # Count nodes per file (each PDF page becomes a separate node)
        file_node_counts = defaultdict(int)
        for doc in documents:
            sp_id = doc['sharepoint_id']
            if sp_id and sp_id != "None":
                file_node_counts[sp_id] += 1
        
        print(f"\n📊 Nodes per file:")
        for sp_id, count in file_node_counts.items():
            file_doc = unique_files.get(sp_id)
            if file_doc:
                print(f"   {file_doc['file_name']}: {count} nodes")
        
        print(f"\n💡 Analysis:")
        expected_files = 3  # Based on user's description
        if len(unique_files) > expected_files:
            print(f"   - Found {len(unique_files)} unique files, expected ~{expected_files}")
            print(f"   - This suggests there are {len(unique_files) - expected_files} extra files indexed")
            print(f"   - You may want to clear all documents and re-sync only the target folders")
        else:
            print(f"   - Found {len(unique_files)} unique files, which seems reasonable")
        
    except Exception as e:
        print(f"❌ Error analyzing documents: {e}")

if __name__ == "__main__":
    analyze_indexed_documents()