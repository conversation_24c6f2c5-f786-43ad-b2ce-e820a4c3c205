import os
import shutil
from datetime import datetime
import json

def clear_index():
    """Clear the existing index to prepare for a fresh sync from the correct location."""
    
    print("=" * 80)
    print("CLEAR INDEX UTILITY")
    print("=" * 80)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # Check current index status
    storage_dir = "storage"
    backup_dir = f"storage_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    if not os.path.exists(storage_dir):
        print("❌ No storage directory found. Nothing to clear.")
        return
    
    # Check current document count
    try:
        docstore_path = os.path.join(storage_dir, "docstore.json")
        if os.path.exists(docstore_path):
            with open(docstore_path, 'r') as f:
                docstore_data = json.load(f)
                docs = docstore_data.get('docstore/data', {})
                doc_count = len(docs)
                
                # Count SharePoint vs local docs
                sharepoint_count = sum(1 for doc in docs.values() 
                                     if doc.get('__data__', {}).get('metadata', {}).get('sharepoint_id'))
                
                print(f"\n📊 CURRENT INDEX STATUS:")
                print(f"   Total documents: {doc_count}")
                print(f"   SharePoint documents: {sharepoint_count}")
                print(f"   Local documents: {doc_count - sharepoint_count}")
        else:
            print("\n📊 No docstore.json found")
    except Exception as e:
        print(f"\n⚠️  Error reading current index: {e}")
    
    # Confirm action
    print("\n" + "=" * 80)
    print("⚠️  WARNING: This will delete all indexed documents!")
    print("You will need to re-sync from SharePoint after clearing.")
    print("=" * 80)
    
    response = input("\nDo you want to proceed? (yes/no): ").strip().lower()
    
    if response != 'yes':
        print("\n❌ Operation cancelled.")
        return
    
    # Create backup
    try:
        print(f"\n📦 Creating backup at: {backup_dir}")
        shutil.copytree(storage_dir, backup_dir)
        print("✅ Backup created successfully")
    except Exception as e:
        print(f"❌ Error creating backup: {e}")
        response = input("\nContinue without backup? (yes/no): ").strip().lower()
        if response != 'yes':
            print("❌ Operation cancelled.")
            return
    
    # Clear the index
    try:
        print("\n🗑️  Clearing index...")
        
        # Remove all files in storage directory
        for filename in os.listdir(storage_dir):
            file_path = os.path.join(storage_dir, filename)
            if os.path.isfile(file_path):
                os.remove(file_path)
                print(f"   Removed: {filename}")
            elif os.path.isdir(file_path) and filename != "data":
                shutil.rmtree(file_path)
                print(f"   Removed directory: {filename}")
        
        print("\n✅ Index cleared successfully!")
        
        print("\n📝 NEXT STEPS:")
        print("1. Update SYNC_TARGET_FOLDER_PATH in .env if needed")
        print("2. Start the app: uvicorn app:app --host localhost --port 8082")
        print("3. Login as admin")
        print("4. Go to SharePoint sync and run manual sync")
        print("5. The app will re-index documents from the correct location")
        
    except Exception as e:
        print(f"\n❌ Error clearing index: {e}")
        print(f"You may need to manually delete files in: {storage_dir}")

if __name__ == "__main__":
    clear_index()