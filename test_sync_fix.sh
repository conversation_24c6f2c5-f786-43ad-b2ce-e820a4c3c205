#!/bin/bash

# Test and Fix SharePoint Sync Path Issue
# This script helps verify the SharePoint sync is pointing to the correct folder

echo "=========================================="
echo "SharePoint Sync Path Verification Script"
echo "=========================================="
echo "Date: $(date)"
echo ""

# Check if app is running
check_app_running() {
    if lsof -i :8082 | grep -q LISTEN; then
        echo "✅ App is running on port 8082"
        return 0
    else
        echo "❌ App is not running on port 8082"
        return 1
    fi
}

# Show current configuration
echo "📋 Current Configuration:"
echo "-------------------------"

# Check .env file
if [ -f .env ]; then
    echo "SYNC_TARGET_FOLDER_PATH: $(grep SYNC_TARGET_FOLDER_PATH .env | cut -d'=' -f2 || echo 'Not set')"
    echo "SharePoint Site: $(grep SHAREPOINT_SITE_NAME .env | cut -d'=' -f2)"
else
    echo "❌ .env file not found"
fi

# Check current index status
echo ""
echo "📊 Current Index Status:"
if [ -f storage/docstore.json ]; then
    python3 -c "
import json
with open('storage/docstore.json', 'r') as f:
    data = json.load(f)
    docs = data.get('docstore/data', {})
    print(f'Total indexed documents: {len(docs)}')
    sp_count = sum(1 for d in docs.values() if d.get('__data__',{}).get('metadata',{}).get('sharepoint_id'))
    print(f'SharePoint documents: {sp_count}')
    print(f'Local documents: {len(docs) - sp_count}')
"
else
    echo "No index found (storage/docstore.json missing)"
fi

echo ""
echo "=========================================="
echo "Available Actions:"
echo "=========================================="
echo "1. Investigate indexed documents: python investigate_handbook.py"
echo "2. Find handbook in SharePoint: python find_handbook_location.py" 
echo "3. Verify SharePoint folders: python verify_sharepoint_path.py"
echo "4. Clear index for fresh sync: python clear_index.py"
echo "5. Start the app: uvicorn app:app --host localhost --port 8082"
echo ""

if check_app_running; then
    echo "App is running. You can:"
    echo "- Login as admin"
    echo "- Go to SharePoint sync"
    echo "- Check the logs for sync path diagnostics"
else
    echo "App is not running. Start it to perform sync operations."
fi

echo ""
echo "=========================================="
echo "DIAGNOSIS:"
echo "=========================================="
echo "✅ ISSUE IDENTIFIED: The 75 'documents' are actually chunks of ONE file:"
echo "   - DDB Employee Handbook.pdf (SharePoint ID: 01ADQ5T2BBJ4DG5CFI2VD2ERRBHFYVFOSN)"
echo ""
echo "📍 CURRENT STATE:"
echo "   - DDB Group Repository has 2 EMPTY subfolders (Human Resources, Culture Hub)"
echo "   - The handbook file is NO LONGER in those folders"
echo "   - But it's still in the app's index from a previous sync"
echo ""
echo "🔧 SOLUTION:"
echo "   1. Clear the index to remove the outdated handbook"
echo "   2. Re-sync from SharePoint to get current folder contents"
echo "   3. The app will then correctly show only files currently in DDB Group Repository"
echo ""
echo "The sync path is now configurable via SYNC_TARGET_FOLDER_PATH in .env"
echo "=========================================="
