#!/usr/bin/env python3
"""
Comprehensive cleanup of ALL document issues found in the scan.
This will fix the deletion problems permanently.
"""

import json
import os
from pathlib import Path

def cleanup_all_document_issues():
    """Remove all problematic documents found in the scan"""
    
    # Documents to remove completely
    optimising_sharepoint_id = "01ADQ5T2BXLEAHYWCOOFEIGCS3ACHLBHTS"
    evolution_sharepoint_id = "01ADQ5T2DWCE4JGYNHOVHYE5OLWU7ZIH7E"
    
    # Node IDs to remove (from scan results)
    nodes_to_remove = [
        # Optimising document nodes
        "948585a8-67e5-4669-a972-6f07683e48ff",
        "bdf22644-db8a-4ec4-b899-83f074ef656b", 
        "59349b95-f84c-4522-b710-5bfaf88dbce8",
        "e215e020-ae74-4aef-aa82-93934f5f4663",
        
        # Evolution document nodes  
        "6ed8c072-bf27-4b32-ad40-5d49f66b700f",
        "f8fa903b-6e7b-4539-b751-f1fb37922d51",
        "310cd464-c71f-4ef9-af1b-6d7ede9f50d2",
        "902a8b06-db2d-4886-ae77-16f4c6f5c13a",
        "5b12bea4-ad39-4e0a-8c58-4f916b95baec"
    ]
    
    print("=== COMPREHENSIVE DOCUMENT CLEANUP ===\n")
    
    # 1. Clean up docstore.json
    print("1. Cleaning up docstore.json...")
    docstore_file = Path("storage/docstore.json")
    if docstore_file.exists():
        try:
            with open(docstore_file, 'r') as f:
                docstore_data = json.load(f)
            
            removed_count = 0
            for node_id in nodes_to_remove:
                if node_id in docstore_data.get("docstore/data", {}):
                    del docstore_data["docstore/data"][node_id]
                    removed_count += 1
                    print(f"   ✅ Removed {node_id[:8]}... from docstore")
            
            if removed_count > 0:
                with open(docstore_file, 'w') as f:
                    json.dump(docstore_data, f)
                print(f"   📝 Updated docstore.json (removed {removed_count} nodes)")
            else:
                print("   ℹ️  No nodes found to remove from docstore")
                
        except Exception as e:
            print(f"   ❌ Error updating docstore: {e}")
    
    # 2. Clean up vector store
    print("\n2. Cleaning up vector store...")
    vector_file = Path("storage/default__vector_store.json")
    if vector_file.exists():
        try:
            with open(vector_file, 'r') as f:
                vector_data = json.load(f)
            
            removed_count = 0
            if "embedding_dict" in vector_data:
                for node_id in nodes_to_remove:
                    if node_id in vector_data["embedding_dict"]:
                        del vector_data["embedding_dict"][node_id]
                        removed_count += 1
                        print(f"   ✅ Removed {node_id[:8]}... from vector store")
            
            if removed_count > 0:
                with open(vector_file, 'w') as f:
                    json.dump(vector_data, f)
                print(f"   📝 Updated vector store (removed {removed_count} embeddings)")
            else:
                print("   ℹ️  No embeddings found to remove from vector store")
                
        except Exception as e:
            print(f"   ❌ Error updating vector store: {e}")
    
    # 3. Clean up index store
    print("\n3. Cleaning up index store...")
    index_file = Path("storage/index_store.json")
    if index_file.exists():
        try:
            with open(index_file, 'r') as f:
                index_data = json.load(f)
            
            removed_count = 0
            for store_key, store_data in index_data.get("index_store/data", {}).items():
                if isinstance(store_data, dict) and "__data__" in store_data:
                    try:
                        store_content = json.loads(store_data["__data__"])
                        
                        # Remove from nodes_dict
                        if "nodes_dict" in store_content:
                            for node_id in nodes_to_remove:
                                if node_id in store_content["nodes_dict"]:
                                    del store_content["nodes_dict"][node_id]
                                    removed_count += 1
                                    print(f"   ✅ Removed {node_id[:8]}... from index structure")
                        
                        # Update the stored data
                        store_data["__data__"] = json.dumps(store_content)
                        
                    except Exception as e:
                        print(f"   ⚠️  Error processing store {store_key}: {e}")
            
            if removed_count > 0:
                with open(index_file, 'w') as f:
                    json.dump(index_data, f)
                print(f"   📝 Updated index store (removed {removed_count} references)")
            else:
                print("   ℹ️  No references found to remove from index store")
                
        except Exception as e:
            print(f"   ❌ Error updating index store: {e}")
    
    # 4. Final verification
    print("\n4. Verifying cleanup...")
    verification_passed = True
    
    files_to_check = [
        "storage/docstore.json",
        "storage/default__vector_store.json", 
        "storage/index_store.json"
    ]
    
    for file_path in files_to_check:
        if Path(file_path).exists():
            try:
                with open(file_path, 'r') as f:
                    content = f.read()
                
                # Check for problematic SharePoint IDs
                optimising_found = optimising_sharepoint_id in content
                evolution_found = evolution_sharepoint_id in content
                
                if optimising_found or evolution_found:
                    print(f"   ⚠️  Still found references in {file_path}:")
                    if optimising_found:
                        print(f"       - Optimising SharePoint ID still present")
                    if evolution_found:
                        print(f"       - Evolution SharePoint ID still present")
                    verification_passed = False
                else:
                    print(f"   ✅ {file_path} is clean")
                    
            except Exception as e:
                print(f"   ❌ Error checking {file_path}: {e}")
                verification_passed = False
    
    print(f"\n=== CLEANUP RESULTS ===")
    if verification_passed:
        print("🎉 SUCCESS: All problematic documents have been removed!")
        print("📊 Expected results after restart:")
        print("   - CASTING document: ✅ (4 nodes)")
        print("   - SILENCE document: ✅ (2 nodes)")
        print("   - 6Cs document: ✅ (2 nodes)")
        print("   - Employee Handbook: ✅ (75 nodes)")
        print("   - Optimising document: ❌ (removed)")
        print("   - Evolution document: ❌ (removed)")
        print("   - Total expected nodes: ~83 (down from 92)")
        print("\n🔄 Restart the application to see changes")
        print("🧪 Test querying 'Optimising' - should return no results instead of hanging")
        return True
    else:
        print("⚠️  Some cleanup issues remain - manual intervention may be needed")
        return False

if __name__ == "__main__":
    success = cleanup_all_document_issues()
    if success:
        print("\n✨ Cleanup complete! The sync deletion issues should now be resolved.")
    else:
        print("\n🔧 Additional cleanup may be required.")