#!/usr/bin/env python3
"""
Final complete removal of Evolution document from storage files.
This script will remove ALL references to the Evolution document.
"""

import json
import os
from pathlib import Path

def remove_evolution_completely():
    """Remove Evolution document completely from all storage files"""
    
    # Evolution document identifiers
    evolution_sharepoint_id = "01ADQ5T2BG2XKASDWY6FCYVOP6PZT4NS7A"
    evolution_doc_id = "8091ab56-ac07-4fc2-941a-c974ab16b502"
    evolution_node_ids = [
        "917680d6-8a8e-42cc-8616-fefa0b3ec693",  # Page 1
        "239473a3-6445-44ac-8f99-09f5f5ecc23e",  # Page 1 continuation
        "79496c8d-c216-4930-9875-530291b36178",  # Page 2
        "0d8ffc4e-876d-449b-9500-e076d941a38f",  # Page 3
        "8091ab56-ac07-4fc2-941a-c974ab16b502",  # Page 4 (ref_doc_id)
    ]
    
    print("=== COMPLETE EVOLUTION DOCUMENT REMOVAL ===\n")
    
    # 1. Remove from docstore.json
    print("1. Removing from docstore.json...")
    docstore_file = Path("storage/docstore.json")
    if docstore_file.exists():
        try:
            with open(docstore_file, 'r') as f:
                docstore_data = json.load(f)
            
            removed_count = 0
            for node_id in evolution_node_ids:
                if node_id in docstore_data.get("docstore/data", {}):
                    del docstore_data["docstore/data"][node_id]
                    removed_count += 1
                    print(f"   ✅ Removed {node_id}")
            
            if removed_count > 0:
                with open(docstore_file, 'w') as f:
                    json.dump(docstore_data, f)
                print(f"   📝 Updated docstore.json (removed {removed_count} nodes)")
            else:
                print("   ℹ️  No Evolution nodes found in docstore")
                
        except Exception as e:
            print(f"   ❌ Error updating docstore: {e}")
    
    # 2. Remove from vector store
    print("\n2. Removing from vector store...")
    vector_file = Path("storage/default__vector_store.json")
    if vector_file.exists():
        try:
            with open(vector_file, 'r') as f:
                vector_data = json.load(f)
            
            removed_count = 0
            if "embedding_dict" in vector_data:
                for node_id in evolution_node_ids:
                    if node_id in vector_data["embedding_dict"]:
                        del vector_data["embedding_dict"][node_id]
                        removed_count += 1
                        print(f"   ✅ Removed {node_id}")
            
            if removed_count > 0:
                with open(vector_file, 'w') as f:
                    json.dump(vector_data, f)
                print(f"   📝 Updated vector store (removed {removed_count} embeddings)")
            else:
                print("   ℹ️  No Evolution embeddings found in vector store")
                
        except Exception as e:
            print(f"   ❌ Error updating vector store: {e}")
    
    # 3. Verify removal
    print("\n3. Verifying removal...")
    evolution_found = False
    
    files_to_check = [
        "storage/docstore.json",
        "storage/default__vector_store.json",
        "storage/index_store.json"
    ]
    
    for file_path in files_to_check:
        if Path(file_path).exists():
            try:
                with open(file_path, 'r') as f:
                    content = f.read()
                
                if evolution_sharepoint_id in content or evolution_doc_id in content:
                    print(f"   ⚠️  Evolution references still found in {file_path}")
                    evolution_found = True
                else:
                    print(f"   ✅ {file_path} is clean")
            except Exception as e:
                print(f"   ❌ Error checking {file_path}: {e}")
    
    if not evolution_found:
        print("\n🎉 SUCCESS: Evolution document completely removed from all storage files!")
        print("🔄 Restart the application to see the changes")
        return True
    else:
        print("\n⚠️  Some Evolution references may still remain")
        return False

if __name__ == "__main__":
    success = remove_evolution_completely()
    if success:
        print("\n📊 Expected result after restart:")
        print("   - Total unique files: 3")
        print("   - CASTING document: ✅")
        print("   - OPTIMISING document: ✅") 
        print("   - Employee Handbook: ✅")
        print("   - Evolution document: ❌ (removed)")
        print("   - Total nodes: ~83 (down from 87)")
    else:
        print("\n🔧 Manual intervention may be required")