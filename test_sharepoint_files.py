import asyncio
import os
from dotenv import load_dotenv
from sharepoint_client import SharePointClient
import json
from datetime import datetime

load_dotenv()

async def list_ddb_repository_contents():
    """List all folders and files in the DDB Group Repository."""
    
    # Initialize SharePoint client
    client = SharePointClient(
        client_id=os.getenv('MS_CLIENT_ID'),
        client_secret=os.getenv('MS_CLIENT_SECRET'),
        tenant_id=os.getenv('MS_TENANT_ID')
    )
    
    # Known working drive ID from the app
    drive_id = "b!8qRbvGPCU0vmvPqIMdU1CVj1Kch1aZKsPoMqmcF7S3A5GP8ezewSrgF4XXBsT-a"
    
    print("=" * 80)
    print("DDB GROUP REPOSITORY CONTENTS")
    print("=" * 80)
    print(f"Drive ID: {drive_id}")
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # Get access token (you'll need to provide this)
    token = input("Please enter your Microsoft access token: ").strip()
    
    if not token:
        print("ERROR: No token provided")
        return
    
    file_count = 0
    folder_count = 0
    
    async def list_folder_contents(folder_path, indent_level=0):
        """Recursively list folder contents."""
        nonlocal file_count, folder_count
        
        indent = "  " * indent_level
        
        try:
            # Get items in the current folder
            items = await client.list_files(
                drive_id=drive_id,
                token=token,
                folder_path=folder_path
            )
            
            # Separate files and folders
            files = []
            folders = []
            
            for item in items:
                if item.get('file'):
                    files.append(item)
                elif item.get('folder'):
                    folders.append(item)
            
            # Print folder info
            folder_name = folder_path.split('/')[-1] if '/' in folder_path else folder_path
            print(f"{indent}📁 {folder_name}/ ({len(files)} files, {len(folders)} subfolders)")
            
            # List files in this folder
            for file in files:
                file_count += 1
                file_name = file.get('name', 'Unknown')
                file_size = file.get('size', 0)
                file_size_mb = file_size / (1024 * 1024)
                modified = file.get('lastModifiedDateTime', 'Unknown')
                
                print(f"{indent}  📄 {file_name} ({file_size_mb:.2f} MB) - Modified: {modified}")
            
            # Recursively process subfolders
            for folder in folders:
                folder_count += 1
                subfolder_name = folder.get('name', 'Unknown')
                subfolder_path = f"{folder_path}/{subfolder_name}"
                
                # Add empty line before subfolder for readability
                if files:  # Only add space if there were files above
                    print()
                
                await list_folder_contents(subfolder_path, indent_level + 1)
            
        except Exception as e:
            print(f"{indent}❌ ERROR accessing {folder_path}: {str(e)}")
    
    # Start listing from DDB Group Repository
    await list_folder_contents("DDB Group Repository")
    
    print("\n" + "=" * 80)
    print(f"SUMMARY:")
    print(f"Total Folders: {folder_count}")
    print(f"Total Files: {file_count}")
    print("=" * 80)

if __name__ == "__main__":
    asyncio.run(list_ddb_repository_contents())