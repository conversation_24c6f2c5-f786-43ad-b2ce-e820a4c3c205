#!/usr/bin/env python3
"""
Final cleanup of Evolution document - remove all nodes from docstore.
"""

import json
import os
from pathlib import Path

def remove_all_evolution_nodes_from_docstore():
    """Remove all Evolution document nodes from docstore"""
    docstore_file = Path("storage/docstore.json")
    backup_file = str(docstore_file) + ".backup"
    
    if not docstore_file.exists():
        print("❌ docstore.json not found")
        return False
    
    try:
        with open(docstore_file, 'r') as f:
            docstore_data = json.load(f)
        
        # All Evolution document node IDs
        evolution_node_ids = [
            "917680d6-8a8e-42cc-8616-fefa0b3ec693",  # Page 1
            "239473a3-6445-44ac-8f99-09f5f5ecc23e",  # Page 1 continuation
            "79496c8d-c216-4930-9875-530291b36178",  # Page 2
            "0d8ffc4e-876d-449b-9500-e076d941a38f",  # Page 3
            "8091ab56-ac07-4fc2-941a-c974ab16b502",  # Page 4 (ref_doc_id)
            # Additional related nodes that might reference Evolution
            "d9e84de1-4e0d-48be-8cc9-b0f891982de8",  # Document node referenced in relationships
            "a236c866-1344-4ad8-bf8a-c463abb0b5ae",  # Page 2 document node
            "9b07a747-0b80-462c-a0c5-599a15ba71ac",  # Page 3 document node
            "9dd86a63-1286-46bf-a031-7f84f616185e",  # Page 4 document node
        ]
        
        print(f"🔍 Removing {len(evolution_node_ids)} Evolution nodes from docstore...")
        
        removed_count = 0
        if "docstore/data" in docstore_data:
            for node_id in evolution_node_ids:
                if node_id in docstore_data["docstore/data"]:
                    del docstore_data["docstore/data"][node_id]
                    removed_count += 1
                    print(f"   ✅ Removed node {node_id}")
                else:
                    print(f"   ⚠️  Node {node_id} not found in docstore")
        
        print(f"\n📊 Summary: Removed {removed_count} nodes from docstore")
        
        if removed_count > 0:
            # Write updated file
            with open(docstore_file, 'w') as f:
                json.dump(docstore_data, f)
            
            print(f"✅ Updated {docstore_file}")
            return True
        else:
            print("❌ No nodes were removed from docstore")
            return False
        
    except Exception as e:
        print(f"❌ Error processing docstore: {e}")
        return False

def remove_evolution_from_index_store():
    """Remove Evolution references from index store"""
    index_file = Path("storage/index_store.json")
    
    if not index_file.exists():
        print("❌ index_store.json not found")
        return False
    
    try:
        with open(index_file, 'r') as f:
            index_data = json.load(f)
        
        evolution_node_ids = [
            "917680d6-8a8e-42cc-8616-fefa0b3ec693",
            "239473a3-6445-44ac-8f99-09f5f5ecc23e", 
            "79496c8d-c216-4930-9875-530291b36178",
            "0d8ffc4e-876d-449b-9500-e076d941a38f",
            "8091ab56-ac07-4fc2-941a-c974ab16b502",
        ]
        
        removed = False
        
        # Check for nodes_dict in index structure
        for store_key, store_data in index_data.get("index_store/data", {}).items():
            if isinstance(store_data, dict) and "__data__" in store_data:
                try:
                    store_content = json.loads(store_data["__data__"])
                    
                    # Remove from nodes_dict
                    if "nodes_dict" in store_content:
                        for node_id in evolution_node_ids:
                            if node_id in store_content["nodes_dict"]:
                                del store_content["nodes_dict"][node_id]
                                removed = True
                                print(f"   ✅ Removed {node_id} from index structure")
                    
                    # Remove from doc_id_dict if it exists
                    if "doc_id_dict" in store_content:
                        doc_id = "8091ab56-ac07-4fc2-941a-c974ab16b502"
                        if doc_id in store_content["doc_id_dict"]:
                            del store_content["doc_id_dict"][doc_id]
                            removed = True
                            print(f"   ✅ Removed {doc_id} from doc_id_dict")
                    
                    # Update the stored data
                    store_data["__data__"] = json.dumps(store_content)
                    
                except Exception as e:
                    print(f"   ⚠️  Error processing store {store_key}: {e}")
        
        if removed:
            # Write updated file
            with open(index_file, 'w') as f:
                json.dump(index_data, f)
            
            print("✅ Updated index_store.json")
        
        return removed
        
    except Exception as e:
        print(f"❌ Error processing index_store.json: {e}")
        return False

def final_verification():
    """Final verification that Evolution document has been completely removed"""
    print("\n🔍 FINAL VERIFICATION: Checking for remaining Evolution references...")
    
    files_to_check = [
        "storage/docstore.json",
        "storage/default__vector_store.json",
        "storage/index_store.json"
    ]
    
    total_issues = 0
    
    for file_path in files_to_check:
        if Path(file_path).exists():
            try:
                with open(file_path, 'r') as f:
                    content = f.read()
                    
                # Check for Evolution document references
                evolution_count = content.upper().count("EVOLUTION")
                doc_id_count = content.count("8091ab56-ac07-4fc2-941a-c974ab16b502")
                
                if evolution_count > 0 or doc_id_count > 0:
                    print(f"   ❌ {file_path}: {evolution_count} EVOLUTION mentions, {doc_id_count} doc ID mentions")
                    total_issues += 1
                else:
                    print(f"   ✅ {file_path}: Clean")
                    
            except Exception as e:
                print(f"   ❌ Error checking {file_path}: {e}")
                total_issues += 1
    
    return total_issues == 0

def main():
    print("=== FINAL EVOLUTION DOCUMENT CLEANUP ===\n")
    
    print("Step 1: Removing all Evolution nodes from docstore...")
    docstore_success = remove_all_evolution_nodes_from_docstore()
    
    print("\nStep 2: Removing Evolution references from index store...")
    index_success = remove_evolution_from_index_store()
    
    print("\nStep 3: Final verification...")
    verification_success = final_verification()
    
    print(f"\n=== FINAL RESULTS ===")
    if verification_success:
        print("🎉 Evolution document completely removed from all storage components!")
        print("📱 The document should no longer appear in the sidebar")
        print("🔄 Restart the application to see the changes")
    else:
        print("⚠️  Some Evolution references may still remain")
        print("🔧 Check the verification output above for details")

if __name__ == "__main__":
    main()