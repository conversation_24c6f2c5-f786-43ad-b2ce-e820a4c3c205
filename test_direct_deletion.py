#!/usr/bin/env python3
"""
Test the robust deletion functionality by directly calling the deletion functions.
This avoids API authentication issues.
"""

import asyncio
import sys
import os
import logging
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent))

# Set up environment variables from .env file
from dotenv import load_dotenv
load_dotenv()

# Import the required components
from llama_index.core import StorageContext, load_index_from_storage, VectorStoreIndex
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.llms.openai import OpenAI as LlamaOpenAI
from llama_index.core import Settings as LlamaSettings
from config import settings
import openai

async def test_direct_deletion():
    """Test deletion by directly loading the index and calling deletion functions"""
    
    print("=== TESTING DIRECT DELETION ===\n")
    
    try:
        # Initialize OpenAI
        print("1. Initializing models...")
        openai.api_key = settings.OPENAI_API_KEY.get_secret_value()
        embed_model = OpenAIEmbedding(model="text-embedding-3-small")
        llm = LlamaOpenAI(model=settings.OPENAI_MODEL)
        
        # Set global settings
        LlamaSettings.llm = llm
        LlamaSettings.embed_model = embed_model
        print("   ✅ Models initialized")
        
        # Load index
        print("2. Loading index...")
        storage_context = StorageContext.from_defaults(persist_dir=str(settings.STORAGE_DIR))
        index = load_index_from_storage(storage_context)
        print(f"   ✅ Index loaded")
        
        # Check documents before deletion
        print("3. Checking documents before deletion...")
        docs_before = {}
        evolution_nodes = []
        optimising_nodes = []
        
        if hasattr(index, "docstore") and hasattr(index.docstore, "docs"):
            for node_id, doc_info in index.docstore.docs.items():
                file_name = doc_info.metadata.get("file_name", "Unknown") if doc_info.metadata else "Unknown"
                sharepoint_id = doc_info.metadata.get("sharepoint_id") if doc_info.metadata else None
                docs_before[node_id] = {"file_name": file_name, "sharepoint_id": sharepoint_id}
                
                if "evolution" in file_name.lower():
                    evolution_nodes.append({
                        "node_id": node_id,
                        "file_name": file_name,
                        "sharepoint_id": sharepoint_id
                    })
                    
                if "optimising" in file_name.lower():
                    optimising_nodes.append({
                        "node_id": node_id,
                        "file_name": file_name,
                        "sharepoint_id": sharepoint_id
                    })
        
        total_docs_before = len(docs_before)
        print(f"   Total documents before: {total_docs_before}")
        print(f"   Evolution nodes found: {len(evolution_nodes)}")
        print(f"   Optimising nodes found: {len(optimising_nodes)}")
        
        if evolution_nodes:
            print("   Evolution nodes:")
            for node in evolution_nodes:
                print(f"     - {node['file_name']} (SharePoint ID: {node['sharepoint_id']})")
                
        if optimising_nodes:
            print("   Optimising nodes:")
            for node in optimising_nodes:
                print(f"     - {node['file_name']} (SharePoint ID: {node['sharepoint_id']})")
        
        # Manual deletion function
        def manual_delete_node(node_id: str) -> bool:
            """Manually delete a node from all storage components"""
            try:
                deleted_from_docstore = False
                deleted_from_vector_store = False
                deleted_from_index_store = False
                
                # Delete from docstore
                if hasattr(index, "docstore") and hasattr(index.docstore, "docs"):
                    if node_id in index.docstore.docs:
                        del index.docstore.docs[node_id]
                        deleted_from_docstore = True
                
                # Delete from vector store
                if hasattr(index, "_vector_store"):
                    vector_store = index._vector_store
                    if hasattr(vector_store, "data") and hasattr(vector_store.data, "embedding_dict"):
                        if node_id in vector_store.data.embedding_dict:
                            del vector_store.data.embedding_dict[node_id]
                            deleted_from_vector_store = True
                    
                    if hasattr(vector_store, "data") and hasattr(vector_store.data, "text_id_to_ref_doc_id"):
                        if node_id in vector_store.data.text_id_to_ref_doc_id:
                            del vector_store.data.text_id_to_ref_doc_id[node_id]
                    
                    if hasattr(vector_store, "data") and hasattr(vector_store.data, "metadata_dict"):
                        if node_id in vector_store.data.metadata_dict:
                            del vector_store.data.metadata_dict[node_id]
                
                # Delete from index store
                if hasattr(index, "_index_struct"):
                    index_struct = index._index_struct
                    if hasattr(index_struct, "nodes_dict") and node_id in index_struct.nodes_dict:
                        del index_struct.nodes_dict[node_id]
                        deleted_from_index_store = True
                
                return deleted_from_docstore or deleted_from_vector_store or deleted_from_index_store
                
            except Exception as e:
                print(f"       ❌ Error deleting node {node_id[:8]}...: {e}")
                return False
        
        # Delete all problematic nodes
        print("\n4. Deleting problematic nodes...")
        all_problematic_nodes = evolution_nodes + optimising_nodes
        deleted_count = 0
        
        for node in all_problematic_nodes:
            node_id = node["node_id"]
            file_name = node["file_name"]
            print(f"   Deleting {file_name} (node {node_id[:8]}...)...")
            
            success = manual_delete_node(node_id)
            if success:
                deleted_count += 1
                print(f"     ✅ Successfully deleted")
            else:
                print(f"     ❌ Failed to delete")
        
        print(f"\n   Deleted {deleted_count}/{len(all_problematic_nodes)} problematic nodes")
        
        # Persist changes
        print("\n5. Persisting changes...")
        index.storage_context.persist(persist_dir=str(settings.STORAGE_DIR))
        print("   ✅ Changes persisted to storage")
        
        # Verify deletion
        print("\n6. Verifying deletion...")
        docs_after = {}
        evolution_nodes_after = []
        optimising_nodes_after = []
        
        if hasattr(index, "docstore") and hasattr(index.docstore, "docs"):
            for node_id, doc_info in index.docstore.docs.items():
                file_name = doc_info.metadata.get("file_name", "Unknown") if doc_info.metadata else "Unknown"
                docs_after[node_id] = file_name
                
                if "evolution" in file_name.lower():
                    evolution_nodes_after.append({"node_id": node_id, "file_name": file_name})
                    
                if "optimising" in file_name.lower():
                    optimising_nodes_after.append({"node_id": node_id, "file_name": file_name})
        
        total_docs_after = len(docs_after)
        print(f"   Total documents after: {total_docs_after}")
        print(f"   Evolution nodes remaining: {len(evolution_nodes_after)}")
        print(f"   Optimising nodes remaining: {len(optimising_nodes_after)}")
        
        # Results
        deleted_expected = len(all_problematic_nodes)
        actually_deleted = total_docs_before - total_docs_after
        
        print(f"\n=== DELETION RESULTS ===")
        print(f"Expected to delete: {deleted_expected} nodes")
        print(f"Actually deleted: {actually_deleted} nodes")
        print(f"Documents before: {total_docs_before}")
        print(f"Documents after: {total_docs_after}")
        
        if len(evolution_nodes_after) == 0 and len(optimising_nodes_after) == 0:
            print("🎉 SUCCESS: All problematic documents completely removed!")
            return True
        else:
            print("⚠️  Some problematic documents still remain:")
            for node in evolution_nodes_after + optimising_nodes_after:
                print(f"   - {node['file_name']}")
            return False
            
    except Exception as e:
        print(f"❌ Error in direct deletion test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_direct_deletion())
    if success:
        print("\n✨ Direct deletion test completed successfully!")
        print("🔄 Restart the application to see the results")
    else:
        print("\n🔧 Direct deletion test revealed issues that need attention.")