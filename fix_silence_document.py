#!/usr/bin/env python3
"""
Diagnostic script to identify and fix the Silence document deletion issue.
This will test our enhanced deletion function on the problematic Silence document.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent))

# Set up environment variables from .env file
from dotenv import load_dotenv
load_dotenv()

# Import the required components
from llama_index.core import StorageContext, load_index_from_storage
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.llms.openai import OpenAI as LlamaOpenAI
from llama_index.core import Settings as LlamaSettings
from config import settings
import openai

async def fix_silence_document():
    """Find and fix the Silence document deletion issue"""
    
    print("=== SILENCE DOCUMENT DIAGNOSTIC & FIX ===\n")
    
    try:
        # Initialize OpenAI
        print("1. Initializing models...")
        openai.api_key = settings.OPENAI_API_KEY.get_secret_value()
        embed_model = OpenAIEmbedding(model="text-embedding-3-small")
        llm = LlamaOpenAI(model=settings.OPENAI_MODEL)
        
        # Set global settings
        LlamaSettings.llm = llm
        LlamaSettings.embed_model = embed_model
        print("   ✅ Models initialized")
        
        # Load index
        print("2. Loading current index...")
        storage_context = StorageContext.from_defaults(persist_dir=str(settings.STORAGE_DIR))
        index = load_index_from_storage(storage_context)
        print(f"   ✅ Index loaded")
        
        # Find Silence documents
        print("3. Searching for Silence documents...")
        silence_documents = []
        total_docs = 0
        
        if hasattr(index, "docstore") and hasattr(index.docstore, "docs"):
            total_docs = len(index.docstore.docs)
            
            for node_id, doc_info in index.docstore.docs.items():
                if hasattr(doc_info, 'metadata') and doc_info.metadata:
                    file_name = doc_info.metadata.get("file_name", "")
                    sharepoint_id = doc_info.metadata.get("sharepoint_id")
                    
                    if "silence" in file_name.lower():
                        silence_documents.append({
                            "node_id": node_id,
                            "file_name": file_name,
                            "sharepoint_id": sharepoint_id,
                            "ref_doc_id": doc_info.ref_doc_id if hasattr(doc_info, 'ref_doc_id') else None
                        })
        
        print(f"   Total documents in index: {total_docs}")
        print(f"   Silence documents found: {len(silence_documents)}")
        
        if not silence_documents:
            print("   ✅ No Silence documents found - they may have been already deleted!")
            return True
        
        # Display found Silence documents
        silence_sharepoint_ids = set()
        print("\\n   Found Silence documents:")
        for doc in silence_documents:
            print(f"     - {doc['file_name']}")
            print(f"       Node ID: {doc['node_id']}")
            print(f"       SharePoint ID: {doc['sharepoint_id']}")
            print(f"       Ref Doc ID: {doc['ref_doc_id']}")
            if doc['sharepoint_id']:
                silence_sharepoint_ids.add(doc['sharepoint_id'])
        
        print(f"\\n   Unique SharePoint IDs for Silence documents: {len(silence_sharepoint_ids)}")
        
        # Test our enhanced deletion function
        print("\\n4. Testing enhanced deletion function...")
        
        # Import the deletion function from app
        from app import delete_sharepoint_document_completely
        
        success_count = 0
        for sharepoint_id in silence_sharepoint_ids:
            print(f"\\n   Testing deletion of SharePoint ID: {sharepoint_id}")
            
            try:
                # Count nodes before deletion
                nodes_before = len([doc for doc in silence_documents if doc['sharepoint_id'] == sharepoint_id])
                print(f"     Nodes for this SharePoint ID: {nodes_before}")
                
                # Call our enhanced deletion function (but don't actually delete yet)
                print(f"     Would call: delete_sharepoint_document_completely('{sharepoint_id}')")
                
                # For now, let's just simulate the call and show what would happen
                ref_docs_to_delete = set()
                for doc in silence_documents:
                    if doc['sharepoint_id'] == sharepoint_id and doc['ref_doc_id']:
                        ref_docs_to_delete.add(doc['ref_doc_id'])
                
                print(f"     Reference documents that would be deleted: {len(ref_docs_to_delete)}")
                for ref_doc_id in ref_docs_to_delete:
                    print(f"       - {ref_doc_id}")
                
                success_count += 1
                
            except Exception as e:
                print(f"     ❌ Error testing deletion for {sharepoint_id}: {e}")
        
        print(f"\\n5. Deletion test results:")
        print(f"   SharePoint IDs tested: {len(silence_sharepoint_ids)}")
        print(f"   Successful tests: {success_count}")
        
        if success_count == len(silence_sharepoint_ids):
            print("\\n✅ All Silence documents can be processed by our enhanced deletion function!")
            print("\\n🔧 NEXT STEPS:")
            print("   1. Restart the application to load the fixed deletion logic")
            print("   2. Run a manual sync to trigger the enhanced deletion")
            print("   3. The Silence documents should be completely removed")
            return True
        else:
            print("\\n⚠️  Some issues found with deletion testing")
            return False
            
    except Exception as e:
        print(f"❌ Error in Silence document diagnostic: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(fix_silence_document())
    if success:
        print("\\n✨ Silence document diagnostic completed successfully!")
        print("\\nThe enhanced deletion function should now work properly for Silence documents.")
        print("Restart the application and run a sync to test the fix.")
    else:
        print("\\n🔧 Silence document diagnostic revealed issues that need attention.")