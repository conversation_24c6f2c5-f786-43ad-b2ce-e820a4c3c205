#!/usr/bin/env python3
"""
Comprehensive deletion fix that properly removes documents using LlamaIndex's methods.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent))

# Set up environment variables from .env file
from dotenv import load_dotenv
load_dotenv()

# Import the required components
from llama_index.core import StorageContext, load_index_from_storage
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.llms.openai import OpenAI as LlamaOpenAI
from llama_index.core import Settings as LlamaSettings
from config import settings
import openai

async def comprehensive_deletion_fix():
    """Fix deletion by using LlamaIndex's proper deletion methods"""
    
    print("=== COMPREHENSIVE DELETION FIX ===\n")
    
    try:
        # Initialize OpenAI
        print("1. Initializing models...")
        openai.api_key = settings.OPENAI_API_KEY.get_secret_value()
        embed_model = OpenAIEmbedding(model="text-embedding-3-small")
        llm = LlamaOpenAI(model=settings.OPENAI_MODEL)
        
        # Set global settings
        LlamaSettings.llm = llm
        LlamaSettings.embed_model = embed_model
        print("   ✅ Models initialized")
        
        # Load index
        print("2. Loading index...")
        storage_context = StorageContext.from_defaults(persist_dir=str(settings.STORAGE_DIR))
        index = load_index_from_storage(storage_context)
        print(f"   ✅ Index loaded")
        
        # Find all Evolution and Optimising documents by SharePoint ID
        print("3. Finding documents to delete...")
        
        evolution_sharepoint_id = "01ADQ5T2DWCE4JGYNHOVHYE5OLWU7ZIH7E"
        optimising_sharepoint_id = "01ADQ5T2BXLEAHYWCOOFEIGCS3ACHLBHTS"
        
        docs_to_delete = set()  # Use set to avoid duplicates
        
        # Find all reference document IDs that match these SharePoint IDs
        if hasattr(index, "docstore") and hasattr(index.docstore, "docs"):
            for node_id, doc_info in index.docstore.docs.items():
                if hasattr(doc_info, 'metadata') and doc_info.metadata:
                    sharepoint_id = doc_info.metadata.get("sharepoint_id")
                    if sharepoint_id in [evolution_sharepoint_id, optimising_sharepoint_id]:
                        # Get the reference document ID
                        ref_doc_id = doc_info.ref_doc_id if hasattr(doc_info, 'ref_doc_id') else None
                        if ref_doc_id:
                            docs_to_delete.add(ref_doc_id)
                            file_name = doc_info.metadata.get("file_name", "Unknown")
                            print(f"   Found document to delete: {file_name} (ref_doc_id: {ref_doc_id})")
        
        if not docs_to_delete:
            print("   ✅ No problematic documents found - they may have been already deleted!")
            return True
        
        print(f"   Total unique documents to delete: {len(docs_to_delete)}")
        
        # Count documents before deletion
        docs_before = len(index.docstore.docs) if hasattr(index, "docstore") and hasattr(index.docstore, "docs") else 0
        print(f"   Documents before deletion: {docs_before}")
        
        # Delete each document using LlamaIndex's proper method
        print("\\n4. Deleting documents using LlamaIndex's delete_ref_doc...")
        
        successfully_deleted = []
        failed_to_delete = []
        
        for ref_doc_id in docs_to_delete:
            try:
                print(f"   Deleting document {ref_doc_id}...")
                
                # Use LlamaIndex's built-in deletion method
                index.delete_ref_doc(ref_doc_id, delete_from_docstore=True)
                
                # Verify the deletion worked by checking if nodes still exist
                nodes_still_exist = []
                if hasattr(index, "docstore") and hasattr(index.docstore, "docs"):
                    for node_id, doc_info in index.docstore.docs.items():
                        if hasattr(doc_info, 'ref_doc_id') and doc_info.ref_doc_id == ref_doc_id:
                            nodes_still_exist.append(node_id)
                
                if nodes_still_exist:
                    print(f"     ⚠️  {len(nodes_still_exist)} nodes still exist for this document")
                    failed_to_delete.append(ref_doc_id)
                else:
                    print(f"     ✅ Successfully deleted")
                    successfully_deleted.append(ref_doc_id)
                    
            except Exception as e:
                print(f"     ❌ Error deleting {ref_doc_id}: {e}")
                failed_to_delete.append(ref_doc_id)
        
        # Count documents after deletion
        docs_after = len(index.docstore.docs) if hasattr(index, "docstore") and hasattr(index.docstore, "docs") else 0
        actually_deleted = docs_before - docs_after
        
        print(f"\\n   Successfully deleted: {len(successfully_deleted)} documents")
        print(f"   Failed to delete: {len(failed_to_delete)} documents")
        print(f"   Documents after deletion: {docs_after}")
        print(f"   Actually deleted: {actually_deleted} nodes")
        
        # Persist changes
        print("\\n5. Persisting changes...")
        index.storage_context.persist(persist_dir=str(settings.STORAGE_DIR))
        print("   ✅ Changes persisted to storage")
        
        # Reload and verify
        print("\\n6. Reloading index to verify persistence...")
        new_storage_context = StorageContext.from_defaults(persist_dir=str(settings.STORAGE_DIR))
        new_index = load_index_from_storage(new_storage_context)
        
        # Check for remaining problematic documents
        remaining_evolution = []
        remaining_optimising = []
        
        if hasattr(new_index, "docstore") and hasattr(new_index.docstore, "docs"):
            final_count = len(new_index.docstore.docs)
            print(f"   Final document count after reload: {final_count}")
            
            for node_id, doc_info in new_index.docstore.docs.items():
                if hasattr(doc_info, 'metadata') and doc_info.metadata:
                    sharepoint_id = doc_info.metadata.get("sharepoint_id")
                    file_name = doc_info.metadata.get("file_name", "Unknown")
                    
                    if sharepoint_id == evolution_sharepoint_id:
                        remaining_evolution.append(file_name)
                    elif sharepoint_id == optimising_sharepoint_id:
                        remaining_optimising.append(file_name)
        
        print(f"   Remaining Evolution documents: {len(remaining_evolution)}")
        print(f"   Remaining Optimising documents: {len(remaining_optimising)}")
        
        # Final results
        if len(remaining_evolution) == 0 and len(remaining_optimising) == 0:
            print("\\n🎉 SUCCESS: All problematic documents completely removed!")
            print("🔄 You can now restart the application to see the clean results")
            return True
        else:
            print("\\n⚠️  Some problematic documents still remain:")
            for doc in remaining_evolution + remaining_optimising:
                print(f"   - {doc}")
            return False
            
    except Exception as e:
        print(f"❌ Error in comprehensive deletion fix: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(comprehensive_deletion_fix())
    if success:
        print("\\n✨ Comprehensive deletion fix completed successfully!")
    else:
        print("\\n🔧 Comprehensive deletion fix revealed persistent issues.")