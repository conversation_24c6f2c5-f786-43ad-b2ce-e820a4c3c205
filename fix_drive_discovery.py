import asyncio
import os
from datetime import datetime
from sharepoint_client import SharePointClient
from dotenv import load_dotenv
import aiohttp
import json

# Load environment variables
load_dotenv()

async def discover_correct_drive():
    """Discover the correct drive ID for DDB Group Repository."""
    
    print(f"\n{'='*60}")
    print(f"DISCOVERING CORRECT DRIVE ID - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'='*60}\n")
    
    print("This script will help find the correct drive ID for DDB Group Repository")
    print("\nSTEPS:")
    print("1. Login to http://localhost:8082 with Microsoft")
    print("2. Copy your access token from browser (see instructions below)")
    print("3. Paste it here when prompted\n")
    
    print("To get your token:")
    print("- Open browser dev tools (F12)")
    print("- Go to Network tab")
    print("- Click on any request to /api/")
    print("- Look in Request Headers for 'Authorization: Bearer ...'")
    print("- Copy the long token after 'Bearer '\n")
    
    token = input("Paste your Microsoft access token here: ").strip()
    
    if not token:
        print("No token provided. Exiting.")
        return
    
    client = SharePointClient()
    
    print(f"\nSearching for DDB Group Repository...")
    print(f"Target folder: {client.target_folder}")
    print(f"Current drive ID: {client.target_drive_id}")
    
    try:
        # Try the current drive ID first
        print(f"\n1. Testing current drive ID...")
        async with aiohttp.ClientSession() as session:
            test_url = f"https://graph.microsoft.com/v1.0/drives/{client.target_drive_id}/root/children"
            async with session.get(
                test_url,
                headers={"Authorization": f"Bearer {token}"},
            ) as response:
                if response.status == 200:
                    print("✓ Current drive ID is valid!")
                    data = await response.json()
                    items = data.get("value", [])
                    print(f"  Found {len(items)} items in root")
                    
                    # Check for DDB Group Repository
                    for item in items:
                        if item.get("name") == "DDB Group Repository":
                            print(f"✓ Found DDB Group Repository!")
                            print(f"  Folder ID: {item.get('id')}")
                            return
                else:
                    error = await response.text()
                    print(f"✗ Current drive ID failed: {response.status}")
                    print(f"  Error: {error}")
        
        # If current drive ID failed, discover all drives
        print(f"\n2. Discovering all available drives...")
        all_drives = await client.list_all_drives(token)
        print(f"Found {len(all_drives)} total drives")
        
        # Search each drive for DDB Group Repository
        print(f"\n3. Searching for DDB Group Repository in each drive...")
        found_drives = []
        
        for i, drive_info in enumerate(all_drives):
            drive_id = drive_info.get('drive_id')
            drive_name = drive_info.get('drive_name', 'Unknown')
            site_name = drive_info.get('site_name', 'Unknown')
            
            print(f"\nChecking drive {i+1}/{len(all_drives)}: {drive_name} (Site: {site_name})")
            print(f"  Drive ID: {drive_id}")
            
            try:
                # Check if DDB Group Repository exists in this drive
                folder_check = await client.check_folder_exists_in_drive(
                    drive_id, "DDB Group Repository", token
                )
                
                if folder_check.get("found"):
                    print(f"  ✓ FOUND DDB Group Repository!")
                    print(f"    Folder ID: {folder_check.get('folder_id')}")
                    print(f"    Items in folder: {folder_check.get('item_count')}")
                    found_drives.append({
                        "drive_id": drive_id,
                        "drive_name": drive_name,
                        "site_name": site_name,
                        "folder_id": folder_check.get('folder_id'),
                        "item_count": folder_check.get('item_count')
                    })
                else:
                    print(f"  ✗ Not found in this drive")
                    
            except Exception as e:
                print(f"  ✗ Error checking drive: {e}")
        
        # Report results
        print(f"\n{'='*60}")
        print("RESULTS:")
        print(f"{'='*60}")
        
        if found_drives:
            print(f"\nFound DDB Group Repository in {len(found_drives)} drive(s):")
            for drive in found_drives:
                print(f"\nDrive: {drive['drive_name']}")
                print(f"  Site: {drive['site_name']}")
                print(f"  Drive ID: {drive['drive_id']}")
                print(f"  Folder ID: {drive['folder_id']}")
                print(f"  Items: {drive['item_count']}")
            
            # Recommend the correct drive ID
            recommended = found_drives[0]  # Use the first one found
            print(f"\n{'='*60}")
            print("RECOMMENDED CONFIGURATION:")
            print(f"{'='*60}")
            print(f"\nAdd this to your .env file:")
            print(f"SYNC_TARGET_DRIVE_ID={recommended['drive_id']}")
            print(f"\nThis drive contains {recommended['item_count']} items in DDB Group Repository")
            
        else:
            print("\n✗ DDB Group Repository folder not found in any drive!")
            print("Possible issues:")
            print("1. The folder might have been moved or renamed")
            print("2. You might not have access to the folder")
            print("3. The folder might be in a different site")
            
    except Exception as e:
        print(f"\nError during discovery: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(discover_correct_drive())