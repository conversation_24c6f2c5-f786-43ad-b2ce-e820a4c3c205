#!/usr/bin/env python3
"""
Test the robust deletion functionality via API endpoints.
"""

import requests
import json
import time
from requests.auth import HTTPBasicAuth

def test_deletion_via_api():
    """Test deleting problematic documents via API"""
    
    print("=== TESTING ROBUST DELETION VIA API ===\n")
    
    base_url = "http://localhost:8082"
    auth = HTTPBasicAuth("admin", "your-password")
    
    # First, get current document count
    print("1. Getting current document count...")
    try:
        response = requests.get(f"{base_url}/api/documents", auth=auth, timeout=10)
        if response.status_code == 200:
            docs_data = response.json()
            current_count = len(docs_data.get('documents', []))
            print(f"   Current document count: {current_count}")
            
            # Look for problematic documents
            evolution_docs = [doc for doc in docs_data['documents'] if 'evolution' in doc.get('file_name', '').lower()]
            optimising_docs = [doc for doc in docs_data['documents'] if 'optimising' in doc.get('file_name', '').lower()]
            
            print(f"   Found {len(evolution_docs)} Evolution documents")
            print(f"   Found {len(optimising_docs)} Optimising documents")
            
            if evolution_docs:
                print("   Evolution documents:")
                for doc in evolution_docs:
                    print(f"     - {doc.get('file_name')} (SharePoint ID: {doc.get('sharepoint_id', 'N/A')})")
            
            if optimising_docs:
                print("   Optimising documents:")
                for doc in optimising_docs:
                    print(f"     - {doc.get('file_name')} (SharePoint ID: {doc.get('sharepoint_id', 'N/A')})")
                    
            if not evolution_docs and not optimising_docs:
                print("   ✅ No problematic documents found - they may have been already deleted!")
                return True
                
        else:
            print(f"   ❌ Failed to get documents: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error getting documents: {e}")
        return False
    
    print()
    
    # Test cleanup orphaned documents endpoint
    print("2. Testing cleanup orphaned documents endpoint...")
    try:
        response = requests.post(f"{base_url}/api/documents/cleanup-orphaned", auth=auth, timeout=30)
        if response.status_code == 200:
            cleanup_data = response.json()
            print(f"   ✅ Cleanup completed: {cleanup_data}")
        else:
            print(f"   ❌ Cleanup failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error running cleanup: {e}")
    
    print()
    
    # Check document count again
    print("3. Checking document count after cleanup...")
    try:
        response = requests.get(f"{base_url}/api/documents", auth=auth, timeout=10)
        if response.status_code == 200:
            docs_data = response.json()
            new_count = len(docs_data.get('documents', []))
            print(f"   New document count: {new_count}")
            
            # Look for problematic documents again
            evolution_docs = [doc for doc in docs_data['documents'] if 'evolution' in doc.get('file_name', '').lower()]
            optimising_docs = [doc for doc in docs_data['documents'] if 'optimising' in doc.get('file_name', '').lower()]
            
            print(f"   Found {len(evolution_docs)} Evolution documents after cleanup")
            print(f"   Found {len(optimising_docs)} Optimising documents after cleanup")
            
            if evolution_docs or optimising_docs:
                print("   ⚠️  Problematic documents still exist")
                return False
            else:
                print("   ✅ All problematic documents removed!")
                return True
                
        else:
            print(f"   ❌ Failed to get documents: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error getting documents: {e}")
        return False

if __name__ == "__main__":
    success = test_deletion_via_api()
    if success:
        print("\n🎉 Deletion test completed successfully!")
    else:
        print("\n🔧 Deletion test revealed issues that need attention.")