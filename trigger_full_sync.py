import requests
import json
from datetime import datetime

print("=" * 80)
print("TRIGGERING FULL SHAREPOINT SYNC")
print("=" * 80)
print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
print("=" * 80)

# Configuration
BASE_URL = "http://localhost:8082"
DRIVE_ID = "b!8qRbvGPCU0vmvPqIMdU1CVj1Kch1aZKsPoMqmcF7S3A5GP8ezewSrgF4XXBsT-a"
FOLDER_PATH = "DDB Group Repository"

print("\n📋 Configuration:")
print(f"   Drive ID: {DRIVE_ID}")
print(f"   Folder Path: {FOLDER_PATH}")
print(f"   Endpoint: /sync/sharepoint/{DRIVE_ID}")

# First, we need to authenticate and get an MS token
print("\n⚠️  NOTE: This requires you to be logged into the app with Microsoft authentication")
print("If you haven't logged in yet:")
print("1. Go to http://localhost:8082")
print("2. Login with admin credentials")
print("3. Click 'Import from SharePoint'") 
print("4. Login with Microsoft account")
print("5. Then come back and run this script")

input("\nPress Enter when you're ready to trigger the sync...")

# Try to trigger the full sync endpoint
try:
    # We need to pass the MS token from the session
    # Since we can't easily get it from outside, we'll use the browser session
    
    print("\n🔄 Triggering full sync...")
    print("\nSince the manual sync button is broken, please do this manually:")
    print(f"1. Open browser developer tools (F12)")
    print(f"2. Go to the Network tab")
    print(f"3. In the console, run this command:")
    print(f"\n   fetch('{BASE_URL}/sync/sharepoint/{DRIVE_ID}?folder_path={FOLDER_PATH}', {{")
    print(f"       method: 'POST',")
    print(f"       credentials: 'include'")
    print(f"   }}).then(r => r.json()).then(console.log)")
    print(f"\n4. This will trigger the FULL sync that imports new files")
    print(f"5. Check the console for the response")
    
    print("\n💡 Alternative: Fix the button")
    print("The issue is that the 'Manual Sync' button calls the wrong endpoint.")
    print("It calls /api/sync/manual which only deletes orphaned files.")
    print("It should call /sync/sharepoint/{drive_id} which imports AND deletes.")
    
except Exception as e:
    print(f"\n❌ Error: {e}")

print("\n" + "=" * 80)