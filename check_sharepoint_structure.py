import asyncio
import os
import sys
from dotenv import load_dotenv
from sharepoint_client import SharePointClient
from datetime import datetime
import json

load_dotenv()

async def check_ddb_repository_structure():
    """Check the structure of DDB Group Repository using cached token."""
    
    # Initialize SharePoint client
    client = SharePointClient()
    
    # Known working drive ID
    drive_id = "b!8qRbvGPCU0vmvPqIMdU1CVj1Kch1aZKsPoMqmcF7S3A5GP8ezewSrgF4XXBsT-a"
    
    print("=" * 80)
    print("CHECKING DDB GROUP REPOSITORY STRUCTURE")
    print("=" * 80)
    print(f"Drive ID: {drive_id}")
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # Try to get token from cached admin token
    token_cache_path = "storage/data/token_caches/admin_token_cache.json"
    
    try:
        if os.path.exists(token_cache_path):
            with open(token_cache_path, 'r') as f:
                cache_data = json.load(f)
                token = cache_data.get('access_token')
                if token:
                    print("✓ Using cached admin token")
                else:
                    print("❌ No access token in cache")
                    return
        else:
            print("❌ No token cache found at:", token_cache_path)
            print("\nTo get a token:")
            print("1. Login to the app as admin")
            print("2. Navigate to SharePoint import")
            print("3. This will create the token cache")
            return
    except Exception as e:
        print(f"❌ Error reading token cache: {e}")
        return
    
    file_count = 0
    folder_count = 0
    structure = {}
    
    async def scan_folder(folder_path, indent_level=0):
        """Recursively scan folder structure."""
        nonlocal file_count, folder_count
        
        indent = "  " * indent_level
        
        try:
            # Get items in the current folder
            items = await client.list_files(
                drive_id=drive_id,
                token=token,
                folder_path=folder_path
            )
            
            # Separate files and folders
            files = []
            folders = []
            
            for item in items:
                if item.get('file'):
                    files.append(item)
                elif item.get('folder'):
                    folders.append(item)
            
            # Display folder info
            folder_name = folder_path.split('/')[-1] if '/' in folder_path else folder_path
            print(f"\n{indent}📁 {folder_name}/ ({len(files)} files, {len(folders)} subfolders)")
            print(f"{indent}" + "-" * 60)
            
            # Store in structure dict
            current_folder = {
                'files': [],
                'folders': {},
                'file_count': len(files),
                'folder_count': len(folders)
            }
            
            # List files
            if files:
                for file in files:
                    file_count += 1
                    file_info = {
                        'name': file.get('name', 'Unknown'),
                        'id': file.get('id', 'Unknown'),
                        'size': file.get('size', 0),
                        'modified': file.get('lastModifiedDateTime', 'Unknown')
                    }
                    current_folder['files'].append(file_info)
                    
                    size_mb = file_info['size'] / (1024 * 1024)
                    print(f"{indent}  📄 {file_info['name']} ({size_mb:.2f} MB)")
            else:
                print(f"{indent}  (no files)")
            
            # Process subfolders
            for folder in folders:
                folder_count += 1
                subfolder_name = folder.get('name', 'Unknown')
                subfolder_path = f"{folder_path}/{subfolder_name}"
                
                # Recursively scan subfolder
                subfolder_structure = await scan_folder(subfolder_path, indent_level + 1)
                current_folder['folders'][subfolder_name] = subfolder_structure
            
            return current_folder
            
        except Exception as e:
            print(f"{indent}❌ ERROR accessing {folder_path}: {str(e)}")
            return {'error': str(e)}
    
    # Start scanning from DDB Group Repository
    print("\nScanning folder structure...")
    structure['DDB Group Repository'] = await scan_folder("DDB Group Repository")
    
    # Print summary
    print("\n" + "=" * 80)
    print("SUMMARY:")
    print(f"Total Folders: {folder_count}")
    print(f"Total Files: {file_count}")
    print("=" * 80)
    
    # Save structure to file for analysis
    output_file = f"ddb_repository_structure_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(output_file, 'w') as f:
        json.dump({
            'scan_date': datetime.now().isoformat(),
            'drive_id': drive_id,
            'total_files': file_count,
            'total_folders': folder_count,
            'structure': structure
        }, f, indent=2)
    
    print(f"\n✓ Full structure saved to: {output_file}")
    
    # Also check current indexed documents
    print("\n" + "=" * 80)
    print("CHECKING INDEXED DOCUMENTS")
    print("=" * 80)
    
    try:
        # Import app components
        from app import app
        
        # Check if index exists
        storage_dir = "storage"
        if os.path.exists(storage_dir):
            # Count documents in index
            from llama_index.core import StorageContext, load_index_from_storage
            
            storage_context = StorageContext.from_defaults(persist_dir=storage_dir)
            index = load_index_from_storage(storage_context)
            
            indexed_count = 0
            sharepoint_docs = 0
            
            if hasattr(index, "docstore") and hasattr(index.docstore, "docs"):
                for doc_id, doc_info in index.docstore.docs.items():
                    indexed_count += 1
                    if doc_info.metadata and doc_info.metadata.get("sharepoint_id"):
                        sharepoint_docs += 1
            
            print(f"Total Indexed Documents: {indexed_count}")
            print(f"Documents with SharePoint ID: {sharepoint_docs}")
            print(f"Local-only Documents: {indexed_count - sharepoint_docs}")
            
            if file_count != sharepoint_docs:
                print(f"\n⚠️  MISMATCH DETECTED:")
                print(f"   SharePoint files: {file_count}")
                print(f"   Indexed from SharePoint: {sharepoint_docs}")
                print(f"   Difference: {file_count - sharepoint_docs}")
        else:
            print("❌ No index storage found")
            
    except Exception as e:
        print(f"❌ Error checking indexed documents: {e}")
    
    print("=" * 80)

if __name__ == "__main__":
    asyncio.run(check_ddb_repository_structure())