#!/usr/bin/env python3
"""
Test the new robust deletion function for complete SharePoint document removal.
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent))

# Import the app to access the deletion function
from app import delete_sharepoint_document_completely, app

async def test_robust_deletion():
    """Test deleting Optimising and Evolution documents completely"""
    
    print("=== TESTING ROBUST SHAREPOINT DOCUMENT DELETION ===\n")
    
    # SharePoint IDs for problematic documents
    optimising_sharepoint_id = "01ADQ5T2BXLEAHYWCOOFEIGCS3ACHLBHTS"
    evolution_sharepoint_id = "01ADQ5T2DWCE4JGYNHOVHYE5OLWU7ZIH7E"
    
    results = []
    
    # Test 1: Delete Optimising document
    print("1. Testing deletion of Optimising document...")
    try:
        success = await delete_sharepoint_document_completely(optimising_sharepoint_id)
        results.append(("Optimising", success))
        if success:
            print("   ✅ Optimising document deletion successful")
        else:
            print("   ❌ Optimising document deletion failed")
    except Exception as e:
        print(f"   ❌ Error deleting Optimising document: {e}")
        results.append(("Optimising", False))
    
    print()
    
    # Test 2: Delete Evolution document  
    print("2. Testing deletion of Evolution document...")
    try:
        success = await delete_sharepoint_document_completely(evolution_sharepoint_id)
        results.append(("Evolution", success))
        if success:
            print("   ✅ Evolution document deletion successful")
        else:
            print("   ❌ Evolution document deletion failed")
    except Exception as e:
        print(f"   ❌ Error deleting Evolution document: {e}")
        results.append(("Evolution", False))
    
    print()
    
    # Summary
    print("=== DELETION TEST RESULTS ===")
    total_tests = len(results)
    successful_tests = sum(1 for _, success in results if success)
    
    for doc_name, success in results:
        status = "✅ SUCCESS" if success else "❌ FAILED"
        print(f"   {doc_name}: {status}")
    
    print(f"\nOverall: {successful_tests}/{total_tests} deletions successful")
    
    if successful_tests == total_tests:
        print("🎉 All problematic documents should now be completely removed!")
        print("🔄 Restart the application to see the results")
        return True
    else:
        print("⚠️  Some deletions failed - manual intervention may be needed")
        return False

async def main():
    """Main test function"""
    # Note: This script assumes the app is running and index is loaded
    print("📋 This test requires the application to be running with an initialized index")
    print("🔄 Make sure uvicorn is running before executing this test\n")
    
    try:
        success = await test_robust_deletion()
        return success
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

if __name__ == "__main__":
    # Run the test
    success = asyncio.run(main())
    if success:
        print("\n✨ Robust deletion test completed successfully!")
    else:
        print("\n🔧 Additional work may be needed.")