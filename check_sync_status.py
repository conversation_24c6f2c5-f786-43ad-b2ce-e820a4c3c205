import os
import json
from datetime import datetime

print("=" * 80)
print("SYNC STATUS CHECK")
print("=" * 80)
print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
print("=" * 80)

# Check if index exists
if os.path.exists("storage/docstore.json"):
    try:
        with open("storage/docstore.json", 'r') as f:
            data = json.load(f)
            docs = data.get('docstore/data', {})
            print(f"✅ Index exists with {len(docs)} documents")
    except:
        print("✅ Index exists but is empty (good - ready for fresh sync)")
else:
    print("✅ No index found - ready for fresh sync")

# Check backup
backup_dirs = [d for d in os.listdir('.') if d.startswith('storage_backup_')]
if backup_dirs:
    latest_backup = sorted(backup_dirs)[-1]
    print(f"📦 Backup available: {latest_backup}")

print("\n" + "=" * 80)
print("NEXT STEPS FOR MANUAL SYNC:")
print("=" * 80)
print("Since the app is already running on port 8082:")
print("")
print("1. Open your browser and go to: http://localhost:8082")
print("2. Login with credentials:")
print("   Username: admin")
print("   Password: your-password")
print("3. After login, you'll see the main interface")
print("4. Click on 'Import from SharePoint' or 'SharePoint Sync' button")
print("5. Login with your Microsoft account (<EMAIL>)")
print("6. Click 'Manual Sync' to sync from DDB Group Repository")
print("")
print("The sync will:")
print("- Check the DDB Group Repository folder")
print("- Find the 2 empty subfolders (Human Resources, Culture Hub)")
print("- Import any files found (currently none)")
print("- Update the document list to show the current state")
print("")
print("After sync, the app should show 0 documents since the folders are empty.")
print("=" * 80)