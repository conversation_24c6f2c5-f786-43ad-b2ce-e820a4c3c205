#!/usr/bin/env python3
"""
Direct deletion fix for the Evolution document.
This script directly modifies the storage files to remove the document.
"""

import json
import os
from pathlib import Path

def remove_from_docstore():
    """Remove Evolution document from docstore.json"""
    docstore_file = Path("storage/docstore.json")
    if not docstore_file.exists():
        print("❌ docstore.json not found")
        return False
    
    try:
        with open(docstore_file, 'r') as f:
            docstore_data = json.load(f)
        
        evolution_doc_id = "8091ab56-ac07-4fc2-941a-c974ab16b502"
        removed = False
        
        if "docstore/data" in docstore_data:
            if evolution_doc_id in docstore_data["docstore/data"]:
                del docstore_data["docstore/data"][evolution_doc_id]
                removed = True
                print(f"✅ Removed {evolution_doc_id} from docstore")
            else:
                print(f"❌ {evolution_doc_id} not found in docstore")
        
        if removed:
            # Backup original file
            os.rename(docstore_file, str(docstore_file) + ".backup")
            
            # Write updated file
            with open(docstore_file, 'w') as f:
                json.dump(docstore_data, f)
            
            print("✅ Updated docstore.json")
        
        return removed
        
    except Exception as e:
        print(f"❌ Error processing docstore.json: {e}")
        return False

def remove_from_vector_store():
    """Remove Evolution document nodes from vector store"""
    vector_file = Path("storage/default__vector_store.json")
    if not vector_file.exists():
        print("❌ default__vector_store.json not found")
        return False
    
    try:
        with open(vector_file, 'r') as f:
            vector_data = json.load(f)
        
        # Find all node IDs that belong to the Evolution document
        evolution_doc_id = "8091ab56-ac07-4fc2-941a-c974ab16b502"
        nodes_to_remove = []
        
        # First, let's check if we can identify which nodes belong to this document
        # by looking at the docstore for reference
        docstore_file = Path("storage/docstore.json")
        if docstore_file.exists():
            with open(docstore_file, 'r') as f:
                docstore_data = json.load(f)
            
            # Find nodes that reference this document
            for node_id, node_data in docstore_data.get("docstore/data", {}).items():
                if isinstance(node_data, dict) and "__data__" in node_data:
                    try:
                        node_content = json.loads(node_data["__data__"])
                        if node_content.get("ref_doc_id") == evolution_doc_id:
                            nodes_to_remove.append(node_id)
                    except:
                        pass
        
        print(f"Found {len(nodes_to_remove)} nodes to remove from vector store")
        
        # Remove nodes from embedding_dict
        removed_count = 0
        if "embedding_dict" in vector_data:
            for node_id in nodes_to_remove:
                if node_id in vector_data["embedding_dict"]:
                    del vector_data["embedding_dict"][node_id]
                    removed_count += 1
        
        if removed_count > 0:
            # Backup original file
            os.rename(vector_file, str(vector_file) + ".backup")
            
            # Write updated file
            with open(vector_file, 'w') as f:
                json.dump(vector_data, f)
            
            print(f"✅ Removed {removed_count} nodes from vector store")
            return True
        else:
            print("❌ No nodes removed from vector store")
            return False
        
    except Exception as e:
        print(f"❌ Error processing vector store: {e}")
        return False

def remove_from_index_store():
    """Remove Evolution document from index_store.json"""
    index_file = Path("storage/index_store.json")
    if not index_file.exists():
        print("❌ index_store.json not found")
        return False
    
    try:
        with open(index_file, 'r') as f:
            index_data = json.load(f)
        
        evolution_doc_id = "8091ab56-ac07-4fc2-941a-c974ab16b502"
        removed = False
        
        # Check for nodes_dict in index structure
        for store_key, store_data in index_data.get("index_store/data", {}).items():
            if isinstance(store_data, dict) and "__data__" in store_data:
                try:
                    store_content = json.loads(store_data["__data__"])
                    if "nodes_dict" in store_content and evolution_doc_id in store_content["nodes_dict"]:
                        del store_content["nodes_dict"][evolution_doc_id]
                        store_data["__data__"] = json.dumps(store_content)
                        removed = True
                        print(f"✅ Removed {evolution_doc_id} from index structure")
                except:
                    pass
        
        if removed:
            # Backup original file
            os.rename(index_file, str(index_file) + ".backup")
            
            # Write updated file
            with open(index_file, 'w') as f:
                json.dump(index_data, f)
            
            print("✅ Updated index_store.json")
        
        return removed
        
    except Exception as e:
        print(f"❌ Error processing index_store.json: {e}")
        return False

def main():
    print("=== DIRECT DELETION FIX FOR EVOLUTION DOCUMENT ===\n")
    print("This script will directly modify storage files to remove the Evolution document.\n")
    
    # Create backup directory
    backup_dir = Path("storage_backup_" + str(int(time.time())))
    backup_dir.mkdir(exist_ok=True)
    
    results = []
    
    print("1. Removing from docstore...")
    results.append(remove_from_docstore())
    
    print("\n2. Removing from vector store...")
    results.append(remove_from_vector_store())
    
    print("\n3. Removing from index store...")
    results.append(remove_from_index_store())
    
    print(f"\n=== RESULTS ===")
    if any(results):
        print("✅ Evolution document removed from storage files!")
        print("📋 Backup files created with .backup extension")
        print("🔄 Restart the application to see changes")
    else:
        print("❌ No changes made - document may already be deleted or not found")

if __name__ == "__main__":
    import time
    main()