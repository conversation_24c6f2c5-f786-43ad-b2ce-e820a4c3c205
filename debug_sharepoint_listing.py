import asyncio
import os
from datetime import datetime
from sharepoint_client import SharePointClient
from dotenv import load_dotenv
import json

# Load environment variables
load_dotenv()

async def debug_sharepoint_listing():
    """Debug SharePoint listing to understand why files aren't being found."""
    
    print(f"\n{'='*60}")
    print(f"DEBUG SHAREPOINT LISTING - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'='*60}\n")
    
    # Get Microsoft token
    print("NOTE: You need to get a valid Microsoft access token from the browser session")
    print("1. Login to http://localhost:8082 with Microsoft")
    print("2. Open browser dev tools (F12)")
    print("3. Go to Application/Storage -> Cookies")
    print("4. Find and copy the 'session' cookie value")
    print("5. Or use the test_manual_sync.py script to trigger sync and check logs\n")
    
    # For now, let's check the SharePoint client configuration
    client = SharePointClient()
    
    print("SharePoint Client Configuration:")
    print(f"  Site Name: {client.site_name}")
    print(f"  Library Name: {client.library_name}")
    print(f"  Target Folder: {client.target_folder}")
    print(f"  Target Drive ID: {client.target_drive_id}")
    
    # Check environment variables
    print("\nEnvironment Variables:")
    print(f"  SYNC_TARGET_FOLDER_PATH: '{os.getenv('SYNC_TARGET_FOLDER_PATH')}'")
    print(f"  SHAREPOINT_SITE_NAME: '{os.getenv('SHAREPOINT_SITE_NAME')}'")
    print(f"  SHAREPOINT_LIBRARY_NAME: '{os.getenv('SHAREPOINT_LIBRARY_NAME')}'")
    
    # The issue might be in how the path is constructed
    sync_folder = os.getenv('SYNC_TARGET_FOLDER_PATH', 'DDB Group Repository')
    print(f"\nUsing sync folder: '{sync_folder}'")
    
    # Check if it's using a subfolder path
    if '/' in sync_folder:
        print(f"WARNING: Sync folder contains '/' which might cause issues")
        print(f"Consider using just the folder name without path separators")
    
    print("\nPossible issues:")
    print("1. The folder path might need to be just 'DDB Group Repository' without any slashes")
    print("2. The SharePoint permissions might not allow access to subfolders")
    print("3. The folder ID might be needed instead of the folder name")
    
    print("\nTo test manually:")
    print("1. Run the app: uvicorn app:app --host localhost --port 8082 --reload")
    print("2. Login with Microsoft")
    print("3. Go to SharePoint browser and verify you can see files")
    print("4. Click Manual Sync and check the logs")

if __name__ == "__main__":
    asyncio.run(debug_sharepoint_listing())