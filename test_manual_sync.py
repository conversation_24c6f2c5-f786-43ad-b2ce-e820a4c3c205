import asyncio
import aiohttp
import json
from datetime import datetime

async def test_manual_sync():
    """Test the manual sync endpoint and log detailed results."""
    
    print(f"\n{'='*60}")
    print(f"TESTING MANUAL SYNC - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'='*60}\n")
    
    # First, let's get the current auth token
    # You'll need to be logged in via the browser first
    print("NOTE: Make sure you're logged in via the browser at http://localhost:8082")
    print("Press Enter to continue after logging in...")
    input()
    
    url = "http://localhost:8082/api/sync/manual"
    
    # Create session with cookies from browser
    async with aiohttp.ClientSession() as session:
        try:
            print(f"Calling manual sync endpoint: {url}")
            print("This will sync the DDB Group Repository folder...")
            
            async with session.post(url) as response:
                status = response.status
                print(f"\nResponse Status: {status}")
                
                if status == 200:
                    data = await response.json()
                    print(f"\nSync Response:")
                    print(json.dumps(data, indent=2))
                    
                    # Analyze the response
                    print(f"\n{'='*40}")
                    print("SYNC ANALYSIS:")
                    print(f"{'='*40}")
                    print(f"Total files found in SharePoint: {data.get('sharepoint_files_found', 0)}")
                    print(f"Files imported: {data.get('files_imported', 0)}")
                    print(f"Files deleted: {data.get('deleted_count', 0)}")
                    
                    if data.get('sharepoint_files_found', 0) == 0:
                        print("\n⚠️  WARNING: No files found in SharePoint!")
                        print("Possible issues:")
                        print("1. The folder path might be incorrect")
                        print("2. Authentication might have failed")
                        print("3. The folder might be empty")
                    
                    if 'diagnostics' in data:
                        print(f"\nDiagnostics:")
                        print(json.dumps(data['diagnostics'], indent=2))
                        
                else:
                    text = await response.text()
                    print(f"\nError Response: {text}")
                    
        except Exception as e:
            print(f"\nError calling sync endpoint: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_manual_sync())