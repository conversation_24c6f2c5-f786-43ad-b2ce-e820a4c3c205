#!/usr/bin/env python3
"""
Diagnose why manual deletion is not working correctly.
"""

import asyncio
import sys
import os
import json
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent))

# Set up environment variables from .env file
from dotenv import load_dotenv
load_dotenv()

# Import the required components
from llama_index.core import StorageContext, load_index_from_storage
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.llms.openai import OpenAI as LlamaOpenAI
from llama_index.core import Settings as LlamaSettings
from config import settings
import openai

async def diagnose_deletion_issue():
    """Diagnose the deletion mechanism"""
    
    print("=== DIAGNOSING DELETION ISSUE ===\n")
    
    try:
        # Initialize OpenAI
        print("1. Initializing models...")
        openai.api_key = settings.OPENAI_API_KEY.get_secret_value()
        embed_model = OpenAIEmbedding(model="text-embedding-3-small")
        llm = LlamaOpenAI(model=settings.OPENAI_MODEL)
        
        # Set global settings
        LlamaSettings.llm = llm
        LlamaSettings.embed_model = embed_model
        print("   ✅ Models initialized")
        
        # Load index
        print("2. Loading index...")
        storage_context = StorageContext.from_defaults(persist_dir=str(settings.STORAGE_DIR))
        index = load_index_from_storage(storage_context)
        print(f"   ✅ Index loaded")
        
        # Examine index structure in detail
        print("3. Examining index structure...")
        
        print("   Index type:", type(index).__name__)
        print("   Index attributes:", [attr for attr in dir(index) if not attr.startswith('_')])
        
        # Check docstore
        if hasattr(index, "docstore"):
            print("   Docstore type:", type(index.docstore).__name__)
            print("   Docstore has docs:", hasattr(index.docstore, "docs"))
            if hasattr(index.docstore, "docs"):
                print("   Docstore docs count:", len(index.docstore.docs))
        
        # Check vector store
        if hasattr(index, "_vector_store"):
            vector_store = index._vector_store
            print("   Vector store type:", type(vector_store).__name__)
            print("   Vector store attributes:", [attr for attr in dir(vector_store) if not attr.startswith('_')])
            
            if hasattr(vector_store, "data"):
                data = vector_store.data
                print("   Vector store data type:", type(data).__name__)
                print("   Vector store data attributes:", [attr for attr in dir(data) if not attr.startswith('_')])
                
                if hasattr(data, "embedding_dict"):
                    print("   Embedding dict count:", len(data.embedding_dict))
                if hasattr(data, "text_id_to_ref_doc_id"):
                    print("   Text ID to ref doc ID count:", len(data.text_id_to_ref_doc_id))
                if hasattr(data, "metadata_dict"):
                    print("   Metadata dict count:", len(data.metadata_dict))
        
        # Check index structure
        if hasattr(index, "_index_struct"):
            index_struct = index._index_struct
            print("   Index struct type:", type(index_struct).__name__)
            print("   Index struct attributes:", [attr for attr in dir(index_struct) if not attr.startswith('_')])
            
            if hasattr(index_struct, "nodes_dict"):
                print("   Index struct nodes dict count:", len(index_struct.nodes_dict))
        
        # Find one Evolution node to test deletion on
        print("\n4. Finding a test node for deletion...")
        evolution_node = None
        
        if hasattr(index, "docstore") and hasattr(index.docstore, "docs"):
            for node_id, doc_info in index.docstore.docs.items():
                file_name = doc_info.metadata.get("file_name", "Unknown") if doc_info.metadata else "Unknown"
                if "evolution" in file_name.lower():
                    evolution_node = {
                        "node_id": node_id,
                        "file_name": file_name,
                        "doc_info": doc_info
                    }
                    break
        
        if not evolution_node:
            print("   ❌ No Evolution node found for testing")
            return False
        
        print(f"   Found test node: {evolution_node['file_name']} (ID: {evolution_node['node_id'][:8]}...)")
        
        # Check presence in all storage components BEFORE deletion
        print("\n5. Checking node presence BEFORE deletion...")
        node_id = evolution_node["node_id"]
        
        in_docstore = False
        in_vector_store = False
        in_index_struct = False
        
        # Check docstore
        if hasattr(index, "docstore") and hasattr(index.docstore, "docs"):
            in_docstore = node_id in index.docstore.docs
            print(f"   In docstore: {in_docstore}")
        
        # Check vector store components
        if hasattr(index, "_vector_store") and hasattr(index._vector_store, "data"):
            data = index._vector_store.data
            
            in_embedding_dict = False
            in_text_id_dict = False
            in_metadata_dict = False
            
            if hasattr(data, "embedding_dict"):
                in_embedding_dict = node_id in data.embedding_dict
                print(f"   In embedding_dict: {in_embedding_dict}")
            
            if hasattr(data, "text_id_to_ref_doc_id"):
                in_text_id_dict = node_id in data.text_id_to_ref_doc_id
                print(f"   In text_id_to_ref_doc_id: {in_text_id_dict}")
            
            if hasattr(data, "metadata_dict"):
                in_metadata_dict = node_id in data.metadata_dict
                print(f"   In metadata_dict: {in_metadata_dict}")
            
            in_vector_store = in_embedding_dict or in_text_id_dict or in_metadata_dict
        
        # Check index structure
        if hasattr(index, "_index_struct") and hasattr(index._index_struct, "nodes_dict"):
            in_index_struct = node_id in index._index_struct.nodes_dict
            print(f"   In index_struct.nodes_dict: {in_index_struct}")
        
        # Attempt to delete manually with detailed tracking
        print("\n6. Attempting manual deletion with detailed tracking...")
        
        deletion_results = {
            "docstore": False,
            "embedding_dict": False,
            "text_id_dict": False,
            "metadata_dict": False,
            "index_struct": False
        }
        
        # Delete from docstore
        if hasattr(index, "docstore") and hasattr(index.docstore, "docs") and node_id in index.docstore.docs:
            try:
                del index.docstore.docs[node_id]
                deletion_results["docstore"] = True
                print("   ✅ Deleted from docstore")
            except Exception as e:
                print(f"   ❌ Failed to delete from docstore: {e}")
        
        # Delete from vector store components
        if hasattr(index, "_vector_store") and hasattr(index._vector_store, "data"):
            data = index._vector_store.data
            
            # Delete from embedding_dict
            if hasattr(data, "embedding_dict") and node_id in data.embedding_dict:
                try:
                    del data.embedding_dict[node_id]
                    deletion_results["embedding_dict"] = True
                    print("   ✅ Deleted from embedding_dict")
                except Exception as e:
                    print(f"   ❌ Failed to delete from embedding_dict: {e}")
            
            # Delete from text_id_to_ref_doc_id
            if hasattr(data, "text_id_to_ref_doc_id") and node_id in data.text_id_to_ref_doc_id:
                try:
                    del data.text_id_to_ref_doc_id[node_id]
                    deletion_results["text_id_dict"] = True
                    print("   ✅ Deleted from text_id_to_ref_doc_id")
                except Exception as e:
                    print(f"   ❌ Failed to delete from text_id_to_ref_doc_id: {e}")
            
            # Delete from metadata_dict
            if hasattr(data, "metadata_dict") and node_id in data.metadata_dict:
                try:
                    del data.metadata_dict[node_id]
                    deletion_results["metadata_dict"] = True
                    print("   ✅ Deleted from metadata_dict")
                except Exception as e:
                    print(f"   ❌ Failed to delete from metadata_dict: {e}")
        
        # Delete from index structure
        if hasattr(index, "_index_struct") and hasattr(index._index_struct, "nodes_dict") and node_id in index._index_struct.nodes_dict:
            try:
                del index._index_struct.nodes_dict[node_id]
                deletion_results["index_struct"] = True
                print("   ✅ Deleted from index_struct.nodes_dict")
            except Exception as e:
                print(f"   ❌ Failed to delete from index_struct.nodes_dict: {e}")
        
        # Check presence AFTER deletion
        print("\n7. Checking node presence AFTER deletion...")
        
        # Check docstore
        if hasattr(index, "docstore") and hasattr(index.docstore, "docs"):
            still_in_docstore = node_id in index.docstore.docs
            print(f"   Still in docstore: {still_in_docstore}")
        
        # Check vector store components
        if hasattr(index, "_vector_store") and hasattr(index._vector_store, "data"):
            data = index._vector_store.data
            
            if hasattr(data, "embedding_dict"):
                still_in_embedding = node_id in data.embedding_dict
                print(f"   Still in embedding_dict: {still_in_embedding}")
            
            if hasattr(data, "text_id_to_ref_doc_id"):
                still_in_text_id = node_id in data.text_id_to_ref_doc_id
                print(f"   Still in text_id_to_ref_doc_id: {still_in_text_id}")
            
            if hasattr(data, "metadata_dict"):
                still_in_metadata = node_id in data.metadata_dict
                print(f"   Still in metadata_dict: {still_in_metadata}")
        
        # Check index structure
        if hasattr(index, "_index_struct") and hasattr(index._index_struct, "nodes_dict"):
            still_in_index_struct = node_id in index._index_struct.nodes_dict
            print(f"   Still in index_struct.nodes_dict: {still_in_index_struct}")
        
        # Persist and reload to test persistence
        print("\n8. Testing persistence...")
        print("   Persisting changes...")
        index.storage_context.persist(persist_dir=str(settings.STORAGE_DIR))
        print("   ✅ Changes persisted")
        
        print("   Reloading index from storage...")
        new_storage_context = StorageContext.from_defaults(persist_dir=str(settings.STORAGE_DIR))
        new_index = load_index_from_storage(new_storage_context)
        print("   ✅ Index reloaded")
        
        # Check if node still exists after reload
        print("   Checking if node exists after reload...")
        if hasattr(new_index, "docstore") and hasattr(new_index.docstore, "docs"):
            still_exists_after_reload = node_id in new_index.docstore.docs
            print(f"   Node exists in reloaded index: {still_exists_after_reload}")
            
            if still_exists_after_reload:
                print("   ❌ PROBLEM: Node still exists after deletion and persistence!")
                print("   This indicates that either deletion or persistence is not working correctly.")
            else:
                print("   ✅ SUCCESS: Node was successfully deleted and persisted!")
        
        print(f"\n=== DELETION RESULTS SUMMARY ===")
        print(f"Deletion attempts:")
        for component, success in deletion_results.items():
            status = "✅" if success else "❌"
            print(f"  {status} {component}")
        
        successful_deletions = sum(deletion_results.values())
        total_attempts = len([v for v in deletion_results.values() if v is not False])
        
        if successful_deletions > 0:
            print(f"\nDeleted from {successful_deletions} storage components")
            return True
        else:
            print(f"\nFailed to delete from any storage components")
            return False
            
    except Exception as e:
        print(f"❌ Error in diagnosis: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(diagnose_deletion_issue())
    if success:
        print("\n✨ Diagnosis completed - found deletion mechanism issues!")
    else:
        print("\n🔧 Diagnosis revealed significant problems with deletion.")