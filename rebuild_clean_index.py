#!/usr/bin/env python3
"""
Rebuild the index without the problematic Evolution and Optimising documents.
This is a more radical but reliable approach to fixing the deletion issue.
"""

import asyncio
import sys
import os
import shutil
from pathlib import Path
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent))

# Set up environment variables from .env file
from dotenv import load_dotenv
load_dotenv()

# Import the required components
from llama_index.core import VectorStoreIndex, StorageContext, load_index_from_storage
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.llms.openai import OpenAI as LlamaOpenAI
from llama_index.core import Settings as LlamaSettings, Document
from llama_index.core.schema import NodeWithScore
from config import settings
import openai

async def rebuild_clean_index():
    """Rebuild index excluding problematic documents"""
    
    print("=== REBUILDING CLEAN INDEX ===\\n")
    
    try:
        # Initialize OpenAI
        print("1. Initializing models...")
        openai.api_key = settings.OPENAI_API_KEY.get_secret_value()
        embed_model = OpenAIEmbedding(model="text-embedding-3-small")
        llm = LlamaOpenAI(model=settings.OPENAI_MODEL)
        
        # Set global settings
        LlamaSettings.llm = llm
        LlamaSettings.embed_model = embed_model
        print("   ✅ Models initialized")
        
        # Create backup of current storage
        print("2. Creating backup of current storage...")
        backup_dir = settings.STORAGE_DIR.parent / f"storage_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copytree(settings.STORAGE_DIR, backup_dir)
        print(f"   ✅ Backup created at: {backup_dir}")
        
        # Load current index
        print("3. Loading current index...")
        storage_context = StorageContext.from_defaults(persist_dir=str(settings.STORAGE_DIR))
        index = load_index_from_storage(storage_context)
        print(f"   ✅ Index loaded")
        
        # Identify problematic SharePoint IDs
        evolution_sharepoint_id = "01ADQ5T2DWCE4JGYNHOVHYE5OLWU7ZIH7E"
        optimising_sharepoint_id = "01ADQ5T2BXLEAHYWCOOFEIGCS3ACHLBHTS"
        problematic_ids = {evolution_sharepoint_id, optimising_sharepoint_id}
        
        # Extract clean documents from the current index
        print("4. Extracting clean documents...")
        clean_documents = []
        problematic_count = 0
        clean_count = 0
        
        if hasattr(index, "docstore") and hasattr(index.docstore, "docs"):
            for node_id, doc_info in index.docstore.docs.items():
                if hasattr(doc_info, 'metadata') and doc_info.metadata:
                    sharepoint_id = doc_info.metadata.get("sharepoint_id")
                    file_name = doc_info.metadata.get("file_name", "Unknown")
                    
                    if sharepoint_id in problematic_ids:
                        problematic_count += 1
                        print(f"   ⚠️  Excluding: {file_name} (SharePoint ID: {sharepoint_id})")
                    else:
                        # Create a Document from this node
                        text_content = doc_info.text if hasattr(doc_info, 'text') else ""
                        doc = Document(
                            text=text_content,
                            metadata=doc_info.metadata,
                            excluded_embed_metadata_keys=doc_info.excluded_embed_metadata_keys if hasattr(doc_info, 'excluded_embed_metadata_keys') else [],
                            excluded_llm_metadata_keys=doc_info.excluded_llm_metadata_keys if hasattr(doc_info, 'excluded_llm_metadata_keys') else []
                        )
                        
                        # Preserve the reference document ID
                        if hasattr(doc_info, 'ref_doc_id'):
                            doc.id_ = doc_info.ref_doc_id
                        
                        clean_documents.append(doc)
                        clean_count += 1
                        if clean_count <= 5:  # Show first 5 for verification
                            print(f"   ✅ Including: {file_name}")
        
        print(f"\\n   Total documents processed: {problematic_count + clean_count}")
        print(f"   Problematic documents excluded: {problematic_count}")
        print(f"   Clean documents to include: {clean_count}")
        
        if problematic_count == 0:
            print("   ✅ No problematic documents found - index is already clean!")
            return True
        
        # Clear storage directory (but keep backup)
        print("\\n5. Clearing current storage...")
        for item in settings.STORAGE_DIR.iterdir():
            if item.is_file():
                item.unlink()
            elif item.is_dir():
                shutil.rmtree(item)
        print("   ✅ Storage cleared")
        
        # Create new clean index
        print("6. Creating new clean index...")
        if clean_documents:
            new_index = VectorStoreIndex.from_documents(
                clean_documents,
                storage_context=StorageContext.from_defaults(),
                show_progress=True
            )
            print(f"   ✅ New index created with {len(clean_documents)} documents")
        else:
            # Create empty index if no documents
            new_index = VectorStoreIndex([], storage_context=StorageContext.from_defaults())
            print("   ✅ Empty index created")
        
        # Persist new index
        print("7. Persisting new clean index...")
        new_index.storage_context.persist(persist_dir=str(settings.STORAGE_DIR))
        print("   ✅ New index persisted")
        
        # Verify the new index
        print("\\n8. Verifying new index...")
        verification_storage_context = StorageContext.from_defaults(persist_dir=str(settings.STORAGE_DIR))
        verification_index = load_index_from_storage(verification_storage_context)
        
        # Check for remaining problematic documents
        remaining_evolution = []
        remaining_optimising = []
        total_docs = 0
        
        if hasattr(verification_index, "docstore") and hasattr(verification_index.docstore, "docs"):
            total_docs = len(verification_index.docstore.docs)
            
            for node_id, doc_info in verification_index.docstore.docs.items():
                if hasattr(doc_info, 'metadata') and doc_info.metadata:
                    sharepoint_id = doc_info.metadata.get("sharepoint_id")
                    file_name = doc_info.metadata.get("file_name", "Unknown")
                    
                    if sharepoint_id == evolution_sharepoint_id:
                        remaining_evolution.append(file_name)
                    elif sharepoint_id == optimising_sharepoint_id:
                        remaining_optimising.append(file_name)
        
        print(f"   New index document count: {total_docs}")
        print(f"   Remaining Evolution documents: {len(remaining_evolution)}")
        print(f"   Remaining Optimising documents: {len(remaining_optimising)}")
        
        # Final results
        if len(remaining_evolution) == 0 and len(remaining_optimising) == 0:
            print("\\n🎉 SUCCESS: Clean index created without problematic documents!")
            print(f"📁 Backup of original index saved at: {backup_dir}")
            print("🔄 Restart the application to use the clean index")
            return True
        else:
            print("\\n❌ FAILURE: Problematic documents still found in new index:")
            for doc in remaining_evolution + remaining_optimising:
                print(f"   - {doc}")
            return False
            
    except Exception as e:
        print(f"❌ Error rebuilding clean index: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(rebuild_clean_index())
    if success:
        print("\\n✨ Index rebuild completed successfully!")
        print("\\n📝 IMPORTANT: This permanently removes the problematic documents.")
        print("If you need to recover them, use the backup directory that was created.")
    else:
        print("\\n🔧 Index rebuild failed - check the backup directory for recovery.")