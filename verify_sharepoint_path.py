import asyncio
import os
import sys
from dotenv import load_dotenv
from sharepoint_client import SharePointClient
from datetime import datetime
import json
from config import settings

load_dotenv()

async def verify_sharepoint_path():
    """Verify the SharePoint path configuration and show what's actually in the DDB Group Repository."""
    
    print("=" * 80)
    print("SHAREPOINT PATH VERIFICATION")
    print("=" * 80)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # Show current configuration
    print("\n📋 CURRENT CONFIGURATION:")
    print(f"   SYNC_TARGET_FOLDER_PATH: '{settings.SYNC_TARGET_FOLDER_PATH}'")
    print(f"   SYNC_TARGET_DRIVE_ID: {settings.SYNC_TARGET_DRIVE_ID}")
    print(f"   SharePoint Site: {os.getenv('SHAREPOINT_SITE_NAME')}")
    print(f"   Library Name: {os.getenv('SHAREPOINT_LIBRARY_NAME')}")
    
    # Initialize SharePoint client
    client = SharePointClient()
    print(f"\n   Client target folder: '{client.target_folder}'")
    print(f"   Client target drive: {client.target_drive_id}")
    
    # Try to get token
    print("\n" + "=" * 80)
    print("CHECKING FOR ACCESS TOKEN...")
    print("=" * 80)
    
    token = None
    # First try to get from environment (for testing)
    if len(sys.argv) > 1:
        token = sys.argv[1]
        print("✓ Using token from command line argument")
    else:
        # Try to get from cached admin token
        token_cache_path = "storage/data/token_caches/admin_token_cache.json"
        try:
            if os.path.exists(token_cache_path):
                with open(token_cache_path, 'r') as f:
                    cache_data = json.load(f)
                    token = cache_data.get('access_token')
                    if token:
                        print("✓ Using cached admin token")
                    else:
                        print("❌ No access token in cache")
                        return
            else:
                print("❌ No token cache found")
                print("\nTo run this verification:")
                print("1. Start the app and login as admin")
                print("2. OR provide a token as argument: python verify_sharepoint_path.py <token>")
                return
        except Exception as e:
            print(f"❌ Error reading token cache: {e}")
            return
    
    if not token:
        print("❌ No access token available")
        return
    
    # List contents of different possible paths
    print("\n" + "=" * 80)
    print("CHECKING SHAREPOINT FOLDER CONTENTS...")
    print("=" * 80)
    
    drive_id = settings.SYNC_TARGET_DRIVE_ID
    
    # Paths to check
    paths_to_check = [
        "",  # Root of drive
        "DDB Group Repository",  # Current hardcoded path
        "2025 DDB Brand Assets",  # The other folder mentioned
    ]
    
    for path in paths_to_check:
        print(f"\n📁 Checking path: '{path}' (empty = root)")
        print("-" * 60)
        
        try:
            items = await client.list_files(
                drive_id=drive_id,
                token=token,
                folder_path=path if path else None
            )
            
            # Separate files and folders
            files = [item for item in items if item.get('file')]
            folders = [item for item in items if item.get('folder')]
            
            print(f"   Found {len(folders)} folders and {len(files)} files")
            
            if folders:
                print("\n   📁 FOLDERS:")
                for folder in folders[:10]:  # Show first 10
                    print(f"      - {folder.get('name')} (ID: {folder.get('id')[:20]}...)")
                if len(folders) > 10:
                    print(f"      ... and {len(folders) - 10} more folders")
            
            if files:
                print("\n   📄 FILES:")
                for file in files[:10]:  # Show first 10
                    size_mb = file.get('size', 0) / (1024 * 1024)
                    print(f"      - {file.get('name')} ({size_mb:.2f} MB)")
                if len(files) > 10:
                    print(f"      ... and {len(files) - 10} more files")
            
            if not files and not folders:
                print("   ⚠️  This folder is empty")
            
        except Exception as e:
            print(f"   ❌ Error accessing path: {e}")
    
    # Specifically check the DDB Group Repository subfolders
    print("\n" + "=" * 80)
    print("CHECKING DDB GROUP REPOSITORY SUBFOLDERS...")
    print("=" * 80)
    
    try:
        # Get contents of DDB Group Repository
        ddb_items = await client.list_files(
            drive_id=drive_id,
            token=token,
            folder_path="DDB Group Repository"
        )
        
        folders = [item for item in ddb_items if item.get('folder')]
        
        for folder in folders:
            folder_name = folder.get('name')
            print(f"\n📂 Checking subfolder: DDB Group Repository/{folder_name}")
            print("-" * 60)
            
            try:
                subfolder_items = await client.list_files(
                    drive_id=drive_id,
                    token=token,
                    folder_path=f"DDB Group Repository/{folder_name}"
                )
                
                sub_files = [item for item in subfolder_items if item.get('file')]
                sub_folders = [item for item in subfolder_items if item.get('folder')]
                
                print(f"   Found {len(sub_folders)} folders and {len(sub_files)} files")
                
                if sub_files:
                    for file in sub_files[:5]:
                        print(f"      - {file.get('name')}")
                    if len(sub_files) > 5:
                        print(f"      ... and {len(sub_files) - 5} more files")
                else:
                    print("   ⚠️  This subfolder is empty")
                
            except Exception as e:
                print(f"   ❌ Error accessing subfolder: {e}")
                
    except Exception as e:
        print(f"❌ Error accessing DDB Group Repository: {e}")
    
    print("\n" + "=" * 80)
    print("RECOMMENDATIONS:")
    print("=" * 80)
    print("1. If 'DDB Group Repository' only has 2 empty subfolders, the 75 indexed documents")
    print("   might be from a different location or from before the folders were emptied.")
    print("2. To fix the sync path, update SYNC_TARGET_FOLDER_PATH in .env file")
    print("3. Clear the existing index and perform a fresh sync")
    print("4. Consider if you need to sync from the root ('') or a specific folder")
    print("=" * 80)

if __name__ == "__main__":
    asyncio.run(verify_sharepoint_path())