#!/usr/bin/env python3
"""
Quick check if the index is properly loaded in the running application.
"""

import asyncio
import sys
import requests
import json
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent))

def check_app_status():
    """Check if the application and index are ready"""
    
    print("=== CHECKING APPLICATION INDEX STATUS ===\n")
    
    try:
        # Test if the application is running
        print("1. Testing application health...")
        health_response = requests.get("http://localhost:8082/health", timeout=5)
        if health_response.status_code == 200:
            print("   ✅ Application is running")
        else:
            print(f"   ❌ Application health check failed: {health_response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Cannot connect to application: {e}")
        return False
    
    try:
        # Test if documents are accessible (index is loaded)
        print("2. Testing document access (index status)...")
        docs_response = requests.get("http://localhost:8082/documents", timeout=10)
        if docs_response.status_code == 200:
            docs_data = docs_response.json()
            doc_count = len(docs_data.get('documents', []))
            print(f"   ✅ Index is accessible with {doc_count} documents")
            return doc_count > 0
        else:
            print(f"   ❌ Document endpoint failed: {docs_response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Cannot access documents: {e}")
        return False

if __name__ == "__main__":
    is_ready = check_app_status()
    if is_ready:
        print("\n✨ Application and index are ready for testing!")
    else:
        print("\n⚠️  Application may still be initializing. Wait a few seconds and try again.")
    
    sys.exit(0 if is_ready else 1)