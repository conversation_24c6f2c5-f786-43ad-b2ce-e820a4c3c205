#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to check for multiple Evolution documents in the index.
This will help identify if there are multiple copies causing the deletion issue.
"""

import os
import sys
import asyncio
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from app import app, settings
from config import Settings

async def check_evolution_documents():
    """Check all Evolution documents in the index."""
    print("=== EVOLUTION DOCUMENT ANALYSIS ===\n")
    
    # Initialize settings
    settings = Settings()
    
    # Check if we have access to the index
    if not hasattr(app.state, 'index') or not app.state.index:
        print("❌ Index not available - application may not be initialized")
        return
    
    # Check docstore for Evolution documents
    evolution_docs = []
    all_docs = []
    
    print("1. Scanning docstore for all documents...")
    
    if hasattr(app.state.index, "docstore") and hasattr(app.state.index.docstore, "docs"):
        for doc_id, doc_info in app.state.index.docstore.docs.items():
            all_docs.append({
                "doc_id": doc_id,
                "metadata": doc_info.metadata if doc_info.metadata else {},
                "file_name": doc_info.metadata.get("file_name", "Unknown") if doc_info.metadata else "Unknown",
                "sharepoint_id": doc_info.metadata.get("sharepoint_id") if doc_info.metadata else None,
                "source": doc_info.metadata.get("source", "Unknown") if doc_info.metadata else "Unknown"
            })
            
            # Check if this is an Evolution document
            file_name = doc_info.metadata.get("file_name", "") if doc_info.metadata else ""
            if "EVOLUTION" in file_name.upper():
                evolution_docs.append({
                    "doc_id": doc_id,
                    "file_name": file_name,
                    "sharepoint_id": doc_info.metadata.get("sharepoint_id") if doc_info.metadata else None,
                    "source": doc_info.metadata.get("source", "Unknown") if doc_info.metadata else "Unknown",
                    "created": doc_info.metadata.get("created", "Unknown") if doc_info.metadata else "Unknown"
                })
    
    print(f"   Total documents in docstore: {len(all_docs)}")
    print(f"   Evolution documents found: {len(evolution_docs)}")
    
    if evolution_docs:
        print("\n2. Evolution document details:")
        for i, doc in enumerate(evolution_docs, 1):
            print(f"   Document {i}:")
            print(f"     - Doc ID: {doc['doc_id']}")
            print(f"     - File Name: {doc['file_name']}")
            print(f"     - SharePoint ID: {doc['sharepoint_id']}")
            print(f"     - Source: {doc['source']}")
            print(f"     - Created: {doc['created']}")
            print()
    else:
        print("\n2. ✅ No Evolution documents found in docstore")
    
    # Check for documents without SharePoint IDs (orphaned)
    orphaned_docs = [doc for doc in all_docs if not doc['sharepoint_id']]
    print(f"\n3. Orphaned documents (no SharePoint ID): {len(orphaned_docs)}")
    
    if orphaned_docs:
        print("   Orphaned document details:")
        for doc in orphaned_docs[:5]:  # Show first 5
            print(f"     - {doc['file_name']} (doc_id: {doc['doc_id'][:8]}...)")
    
    # Check for duplicate SharePoint IDs
    sharepoint_ids = {}
    for doc in all_docs:
        if doc['sharepoint_id']:
            if doc['sharepoint_id'] in sharepoint_ids:
                sharepoint_ids[doc['sharepoint_id']].append(doc)
            else:
                sharepoint_ids[doc['sharepoint_id']] = [doc]
    
    duplicates = {sp_id: docs for sp_id, docs in sharepoint_ids.items() if len(docs) > 1}
    print(f"\n4. Documents with duplicate SharePoint IDs: {len(duplicates)}")
    
    if duplicates:
        print("   Duplicate SharePoint ID details:")
        for sp_id, docs in duplicates.items():
            print(f"     SharePoint ID: {sp_id}")
            for doc in docs:
                print(f"       - {doc['file_name']} (doc_id: {doc['doc_id'][:8]}...)")
    
    print("\n=== ANALYSIS COMPLETE ===")

if __name__ == "__main__":
    # This script needs to be run in the context of the running application
    print("To run this analysis, you need to:")
    print("1. Import this into the running application")
    print("2. Or run it as part of a debugging endpoint")
    print("3. The index needs to be initialized")