import json
import os
from datetime import datetime

def investigate_handbook():
    """Investigate where the DDB Employee Handbook.pdf came from in the index."""
    
    print("=" * 80)
    print("INVESTIGATING DDB EMPLOYEE HANDBOOK.PDF")
    print("=" * 80)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # Check if index exists
    if not os.path.exists("storage/docstore.json"):
        print("❌ No index found at storage/docstore.json")
        return
    
    # Load the index
    with open("storage/docstore.json", 'r') as f:
        docstore_data = json.load(f)
    
    docs = docstore_data.get('docstore/data', {})
    
    # Find documents with "handbook" in the name
    handbook_docs = []
    all_file_names = []
    
    for doc_id, doc_data in docs.items():
        metadata = doc_data.get('__data__', {}).get('metadata', {})
        file_name = metadata.get('file_name', metadata.get('file_path', ''))
        
        # Collect all file names for reference
        if file_name:
            all_file_names.append(file_name)
        
        # Check if this is the handbook
        if 'handbook' in file_name.lower() or 'employee' in file_name.lower():
            handbook_docs.append({
                'doc_id': doc_id,
                'file_name': file_name,
                'metadata': metadata
            })
    
    # Display findings
    if handbook_docs:
        print(f"\n📄 FOUND {len(handbook_docs)} HANDBOOK-RELATED DOCUMENTS:")
        print("-" * 80)
        
        for doc in handbook_docs:
            print(f"\nDocument ID: {doc['doc_id']}")
            print(f"File Name: {doc['file_name']}")
            
            metadata = doc['metadata']
            print("\nMetadata:")
            print(f"  SharePoint ID: {metadata.get('sharepoint_id', 'None')}")
            print(f"  Source: {metadata.get('source', 'Unknown')}")
            print(f"  Drive ID: {metadata.get('drive_id', 'None')}")
            print(f"  Web URL: {metadata.get('web_url', 'None')}")
            print(f"  Created: {metadata.get('created_datetime', 'Unknown')}")
            print(f"  Sync Imported: {metadata.get('sync_imported', False)}")
            
            # Check folder path if available
            if 'folder_path' in metadata:
                print(f"  Folder Path: {metadata['folder_path']}")
            
            # If it has a SharePoint ID, it came from SharePoint
            if metadata.get('sharepoint_id'):
                print(f"\n⚠️  This file was imported from SharePoint!")
                print(f"  SharePoint ID: {metadata['sharepoint_id']}")
                if metadata.get('web_url'):
                    print(f"  It can be accessed at: {metadata['web_url']}")
    else:
        print("\n❌ No handbook documents found in the index")
    
    # Show sample of other files for comparison
    print("\n" + "=" * 80)
    print("SAMPLE OF OTHER INDEXED FILES:")
    print("=" * 80)
    
    # Group files by source
    by_source = {}
    for doc_id, doc_data in docs.items():
        metadata = doc_data.get('__data__', {}).get('metadata', {})
        source = metadata.get('source', 'unknown')
        if source not in by_source:
            by_source[source] = []
        by_source[source].append(metadata.get('file_name', 'Unknown'))
    
    for source, files in by_source.items():
        print(f"\n📁 Source: {source} ({len(files)} files)")
        for file in files[:5]:  # Show first 5
            print(f"   - {file}")
        if len(files) > 5:
            print(f"   ... and {len(files) - 5} more")
    
    # Analysis
    print("\n" + "=" * 80)
    print("ANALYSIS:")
    print("=" * 80)
    
    total_docs = len(docs)
    sp_docs = sum(1 for d in docs.values() if d.get('__data__', {}).get('metadata', {}).get('sharepoint_id'))
    
    print(f"Total documents: {total_docs}")
    print(f"SharePoint documents: {sp_docs}")
    print(f"Local documents: {total_docs - sp_docs}")
    
    print("\n🔍 CONCLUSION:")
    if handbook_docs and any(d['metadata'].get('sharepoint_id') for d in handbook_docs):
        print("The DDB Employee Handbook.pdf WAS imported from SharePoint.")
        print("This means either:")
        print("1. It was in the DDB Group Repository when the sync happened")
        print("2. The app is syncing from a different location than expected")
        print("3. The file exists elsewhere in the SharePoint drive")
        print("\nRECOMMENDATION: Clear the index and re-sync to ensure")
        print("only current files from DDB Group Repository are indexed.")
    else:
        print("The handbook was not found or was uploaded locally.")
    
    print("=" * 80)

if __name__ == "__main__":
    investigate_handbook()