#!/usr/bin/env python3
"""
Enhanced debug script to test the improved deletion functionality.
This tests the new delete_document_with_verification approach and verifies:
1. File detection (finding the deleted file)
2. Deletion execution (with verification and retry logic)  
3. Persistence (storage context synchronization)
4. Frontend refresh (documents endpoint reflecting changes)
"""

import requests
import json
import time

def test_sync_deletion():
    base_url = "http://localhost:8082"
    
    print("=== ENHANCED SYNC DELETION TEST (v2.0) ===\n")
    print("Testing improved deletion with verification and retry logic...")
    
    # 1. Get current document list
    print("\n1. Getting current document list...")
    response = requests.get(f"{base_url}/api/documents")
    if response.status_code == 200:
        docs_before = response.json()
        print(f"   Documents before sync: {docs_before['total_documents']}")
        evolution_before = [doc for doc in docs_before['documents'] if 'EVOLUTION' in doc.get('file_name', '')]
        print(f"   Evolution documents before: {len(evolution_before)}")
        if evolution_before:
            print(f"   Evolution doc SharePoint ID: {evolution_before[0].get('sharepoint_id')}")
            print(f"   Evolution doc ID: {evolution_before[0].get('doc_id', 'Unknown')}")
    else:
        print(f"   ERROR: Failed to get documents: {response.status_code}")
        return
    
    # 2. Trigger manual sync with enhanced logging
    print("\n2. Triggering manual sync with enhanced deletion logic...")
    response = requests.post(f"{base_url}/api/sync/manual")
    if response.status_code == 200:
        sync_result = response.json()
        print(f"   Sync status: {sync_result.get('status')}")
        print(f"   Files found: {sync_result.get('files_found', 0)}")
        print(f"   Files imported: {sync_result.get('files_imported', 0)}")
        print(f"   Files deleted: {sync_result.get('deleted_count', 0)}")
        print(f"   Orphaned deleted: {sync_result.get('orphaned_deleted_count', 0)}")
        print(f"   Total deleted: {sync_result.get('total_deleted', 0)}")
        print(f"   Message: {sync_result.get('message', 'No message')}")
        
        # Check for errors
        if 'errors' in sync_result and sync_result['errors']:
            print(f"   Errors: {sync_result['errors']}")
    else:
        print(f"   ERROR: Sync failed: {response.status_code}")
        print(f"   Response: {response.text}")
        return
    
    # 3. Wait for storage context refresh and persistence
    print("\n3. Waiting for storage context refresh and persistence...")
    time.sleep(3)  # Longer wait for verification and persistence
    
    # 4. Get document list after sync
    print("\n4. Getting document list after sync...")
    response = requests.get(f"{base_url}/api/documents")
    if response.status_code == 200:
        docs_after = response.json()
        print(f"   Documents after sync: {docs_after['total_documents']}")
        evolution_after = [doc for doc in docs_after['documents'] if 'EVOLUTION' in doc.get('file_name', '')]
        print(f"   Evolution documents after: {len(evolution_after)}")
        
        # Compare before and after
        docs_removed = docs_before['total_documents'] - docs_after['total_documents']
        print(f"   Net documents removed: {docs_removed}")
        
        if evolution_before and not evolution_after:
            print("   ✅ SUCCESS: Evolution document was verified as removed!")
            print("   ✅ The enhanced deletion with verification worked!")
        elif evolution_before and evolution_after:
            print("   ❌ FAILURE: Evolution document still exists after enhanced sync")
            print("   ❌ Issue persists despite verification and retry logic")
        else:
            print("   ⚠️  UNCLEAR: Evolution document state changed unexpectedly")
    else:
        print(f"   ERROR: Failed to get documents after sync: {response.status_code}")
    
    # 5. Test queryability with enhanced checking
    print("\n5. Testing document queryability...")
    query = "evolution thinkpiece"
    response = requests.get(f"{base_url}/query/stream", params={"query": query})
    if response.status_code == 200:
        response_text = response.text
        no_info_indicators = [
            "no relevant", "i don't have", "no information available",
            "no documents contain", "not available in the documents"
        ]
        
        if any(phrase in response_text.lower() for phrase in no_info_indicators):
            print("   ✅ SUCCESS: Evolution document is not queryable (verified)")
        else:
            print("   ❌ FAILURE: Evolution document is still queryable")
            print(f"   Query response preview: {response_text[:200]}...")
    else:
        print(f"   ERROR: Query failed: {response.status_code}")
    
    # 6. Additional verification
    print("\n6. Additional verification checks...")
    
    # Check health status
    response = requests.get(f"{base_url}/health")
    if response.status_code == 200:
        print("   ✅ Application health: OK")
    else:
        print("   ❌ Application health: Failed")
    
    print("\n=== ENHANCED TEST COMPLETE ===")
    print("If the Evolution document is still present, the issue may be:")
    print("1. Document not actually deleted from SharePoint")
    print("2. Multiple copies with different SharePoint IDs")
    print("3. Deeper LlamaIndex vector store synchronization issue")
    print("4. Index reloading overriding deletions")

if __name__ == "__main__":
    try:
        test_sync_deletion()
    except Exception as e:
        print(f"ERROR: Test failed with exception: {e}")