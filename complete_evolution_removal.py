#!/usr/bin/env python3
"""
Complete removal of Evolution document from all storage components.
"""

import json
import os
from pathlib import Path

def remove_evolution_nodes_from_vector_store():
    """Remove all Evolution document nodes from vector store"""
    vector_file = Path("storage/default__vector_store.json")
    backup_file = str(vector_file) + ".backup"
    
    if not vector_file.exists():
        print("❌ default__vector_store.json not found")
        return False
    
    # Restore from backup if we already have one
    if Path(backup_file).exists():
        print("📋 Using existing backup file")
        vector_file = Path(backup_file)
    
    try:
        with open(vector_file, 'r') as f:
            vector_data = json.load(f)
        
        # Evolution document node IDs identified from docstore analysis
        evolution_node_ids = [
            "917680d6-8a8e-42cc-8616-fefa0b3ec693",  # Page 1
            "239473a3-6445-44ac-8f99-09f5f5ecc23e",  # Page 1 continuation
            "79496c8d-c216-4930-9875-530291b36178",  # Page 2
            "0d8ffc4e-876d-449b-9500-e076d941a38f",  # Page 3
            "8091ab56-ac07-4fc2-941a-c974ab16b502",  # Page 4 (ref_doc_id)
        ]
        
        print(f"🔍 Looking for {len(evolution_node_ids)} Evolution nodes in vector store...")
        
        # Remove nodes from embedding_dict
        removed_count = 0
        if "embedding_dict" in vector_data:
            for node_id in evolution_node_ids:
                if node_id in vector_data["embedding_dict"]:
                    del vector_data["embedding_dict"][node_id]
                    removed_count += 1
                    print(f"   ✅ Removed node {node_id}")
                else:
                    print(f"   ⚠️  Node {node_id} not found in vector store")
        
        print(f"\n📊 Summary: Removed {removed_count} out of {len(evolution_node_ids)} nodes from vector store")
        
        if removed_count > 0:
            # Write updated file
            output_file = Path("storage/default__vector_store.json")
            with open(output_file, 'w') as f:
                json.dump(vector_data, f)
            
            print(f"✅ Updated {output_file}")
            return True
        else:
            print("❌ No nodes were removed from vector store")
            return False
        
    except Exception as e:
        print(f"❌ Error processing vector store: {e}")
        return False

def verify_removal():
    """Verify that Evolution document has been completely removed"""
    print("\n🔍 VERIFICATION: Checking for remaining Evolution references...")
    
    files_to_check = [
        "storage/docstore.json",
        "storage/default__vector_store.json",
        "storage/index_store.json"
    ]
    
    evolution_references = {}
    
    for file_path in files_to_check:
        if Path(file_path).exists():
            try:
                with open(file_path, 'r') as f:
                    content = f.read()
                    
                # Check for Evolution document references
                evolution_count = content.upper().count("EVOLUTION")
                doc_id_count = content.count("8091ab56-ac07-4fc2-941a-c974ab16b502")
                node_ids_count = 0
                
                # Check for specific node IDs
                evolution_node_ids = [
                    "917680d6-8a8e-42cc-8616-fefa0b3ec693",
                    "239473a3-6445-44ac-8f99-09f5f5ecc23e", 
                    "79496c8d-c216-4930-9875-530291b36178",
                    "0d8ffc4e-876d-449b-9500-e076d941a38f",
                ]
                
                for node_id in evolution_node_ids:
                    if node_id in content:
                        node_ids_count += 1
                
                if evolution_count > 0 or doc_id_count > 0 or node_ids_count > 0:
                    evolution_references[file_path] = {
                        "evolution_mentions": evolution_count,
                        "doc_id_mentions": doc_id_count,
                        "node_id_mentions": node_ids_count
                    }
                    
            except Exception as e:
                print(f"   ❌ Error checking {file_path}: {e}")
    
    if evolution_references:
        print("❌ Evolution document still found in:")
        for file_path, counts in evolution_references.items():
            print(f"   {file_path}:")
            print(f"     - 'EVOLUTION' mentions: {counts['evolution_mentions']}")
            print(f"     - Document ID mentions: {counts['doc_id_mentions']}")
            print(f"     - Node ID mentions: {counts['node_id_mentions']}")
        return False
    else:
        print("✅ Evolution document completely removed from all storage files!")
        return True

def main():
    print("=== COMPLETE EVOLUTION DOCUMENT REMOVAL ===\n")
    
    print("Step 1: Removing Evolution nodes from vector store...")
    vector_success = remove_evolution_nodes_from_vector_store()
    
    print("\nStep 2: Verifying complete removal...")
    verification_success = verify_removal()
    
    print(f"\n=== FINAL RESULTS ===")
    if vector_success and verification_success:
        print("✅ Evolution document successfully removed from all storage components!")
        print("🔄 Restart the application to see the changes in the UI")
    elif vector_success and not verification_success:
        print("⚠️  Vector store updated but some references may remain")
        print("🔄 Restart the application and check if the document is still visible")
    else:
        print("❌ Removal process encountered issues")
        print("🔧 Manual intervention may be required")

if __name__ == "__main__":
    main()