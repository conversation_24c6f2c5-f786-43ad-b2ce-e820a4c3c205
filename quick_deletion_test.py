#!/usr/bin/env python3
"""
Quick test to verify deletion functionality.
"""

import os
import sys
import json
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent))

# Check for Evolution document in stored files
def check_evolution_in_files():
    """Check if Evolution document is still in storage files."""
    storage_dir = Path("storage")
    
    evolution_found = []
    
    # Check docstore.json
    docstore_file = storage_dir / "docstore.json"
    if docstore_file.exists():
        try:
            with open(docstore_file, 'r') as f:
                content = f.read()
                if "EVOLUTION" in content.upper():
                    evolution_found.append("docstore.json")
        except Exception as e:
            print(f"Error reading docstore.json: {e}")
    
    # Check vector store files
    for vector_file in storage_dir.glob("*vector_store.json"):
        try:
            with open(vector_file, 'r') as f:
                content = f.read()
                if "8091ab56-ac07-4fc2-941a-c974ab16b502" in content:
                    evolution_found.append(str(vector_file.name))
        except Exception as e:
            print(f"Error reading {vector_file}: {e}")
    
    return evolution_found

def main():
    print("=== QUICK DELETION VERIFICATION ===\n")
    
    evolution_files = check_evolution_in_files()
    
    if evolution_files:
        print(f"❌ Evolution document still found in: {', '.join(evolution_files)}")
        print("\nThe deletion fix needs to be improved to remove from all storage components.")
        
        # Try to get more details from docstore
        docstore_file = Path("storage/docstore.json")
        if docstore_file.exists():
            try:
                with open(docstore_file, 'r') as f:
                    docstore_data = json.load(f)
                    
                evolution_docs = []
                for doc_id, doc_data in docstore_data.get("docstore/data", {}).items():
                    if isinstance(doc_data, dict) and "__data__" in doc_data:
                        doc_content = json.loads(doc_data["__data__"])
                        if doc_content.get("metadata", {}).get("file_name", ""):
                            if "EVOLUTION" in doc_content["metadata"]["file_name"].upper():
                                evolution_docs.append({
                                    "doc_id": doc_id,
                                    "file_name": doc_content["metadata"]["file_name"],
                                    "sharepoint_id": doc_content["metadata"].get("sharepoint_id")
                                })
                
                if evolution_docs:
                    print(f"\nFound {len(evolution_docs)} Evolution documents in docstore:")
                    for doc in evolution_docs:
                        print(f"  - {doc['file_name']}")
                        print(f"    Doc ID: {doc['doc_id']}")
                        print(f"    SharePoint ID: {doc['sharepoint_id']}")
                        print()
            except Exception as e:
                print(f"Error parsing docstore: {e}")
    else:
        print("✅ Evolution document not found in any storage files!")
        print("The deletion appears to be working correctly.")

if __name__ == "__main__":
    main()