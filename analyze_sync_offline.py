import os
import json
from datetime import datetime
from pathlib import Path
from collections import defaultdict

def analyze_sync_offline():
    """Analyze document sync status offline by examining the index storage."""
    
    print("=" * 80)
    print("OFFLINE SYNC ANALYSIS")
    print("=" * 80)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # Check if index storage exists
    storage_dir = Path("storage")
    
    if not storage_dir.exists():
        print("❌ No storage directory found. The app hasn't indexed any documents yet.")
        return
    
    # Try to load the index metadata
    try:
        # Look for docstore.json which contains document metadata
        docstore_path = storage_dir / "docstore.json"
        
        if docstore_path.exists():
            with open(docstore_path, 'r') as f:
                docstore_data = json.load(f)
            
            # Get document information
            docs = docstore_data.get('docstore/data', {})
            total_docs = len(docs)
            
            print(f"\n📊 INDEXED DOCUMENTS:")
            print(f"   Total documents: {total_docs}")
            
            # Analyze documents
            sharepoint_docs = 0
            local_docs = 0
            docs_by_folder = defaultdict(int)
            docs_by_source = defaultdict(int)
            sharepoint_ids = set()
            
            for doc_id, doc_data in docs.items():
                # Get metadata
                metadata = doc_data.get('__data__', {}).get('metadata', {})
                
                # Check if from SharePoint
                if metadata.get('sharepoint_id'):
                    sharepoint_docs += 1
                    sharepoint_ids.add(metadata['sharepoint_id'])
                else:
                    local_docs += 1
                
                # Track by source
                source = metadata.get('source', 'unknown')
                docs_by_source[source] += 1
                
                # Track by folder/file name
                file_name = metadata.get('file_name', metadata.get('file_path', 'Unknown'))
                if '/' in str(file_name):
                    folder = '/'.join(str(file_name).split('/')[:-1])
                else:
                    folder = 'Root'
                docs_by_folder[folder] += 1
            
            print(f"   From SharePoint: {sharepoint_docs}")
            print(f"   Local uploads: {local_docs}")
            
            print(f"\n📁 DOCUMENTS BY SOURCE:")
            for source, count in sorted(docs_by_source.items()):
                print(f"   {source}: {count}")
            
            if len(docs_by_folder) > 0:
                print(f"\n📁 DOCUMENTS BY FOLDER:")
                for folder, count in sorted(docs_by_folder.items()):
                    print(f"   {folder}: {count}")
            
            # Show some SharePoint IDs as examples
            if sharepoint_ids:
                print(f"\n🔍 SAMPLE SHAREPOINT IDs (first 5):")
                for sp_id in list(sharepoint_ids)[:5]:
                    print(f"   {sp_id}")
            
        else:
            print("❌ No docstore.json found. The index might be in a different format.")
    
    except Exception as e:
        print(f"❌ Error reading index data: {e}")
    
    # Check for any sync logs
    print("\n" + "=" * 80)
    print("CHECKING LOGS FOR SYNC INFORMATION...")
    print("=" * 80)
    
    log_file = Path("app.log")
    if log_file.exists():
        # Get last 100 lines that mention sync or SharePoint
        try:
            with open(log_file, 'r') as f:
                lines = f.readlines()
            
            sync_lines = []
            for line in lines[-500:]:  # Check last 500 lines
                if any(keyword in line.lower() for keyword in ['sync', 'sharepoint', 'ddb group', 'import']):
                    sync_lines.append(line.strip())
            
            if sync_lines:
                print(f"Found {len(sync_lines)} sync-related log entries. Last 10:")
                for line in sync_lines[-10:]:
                    print(f"   {line[:150]}...")  # Truncate long lines
            else:
                print("No recent sync-related log entries found.")
        
        except Exception as e:
            print(f"❌ Error reading logs: {e}")
    else:
        print("No app.log file found.")
    
    print("\n" + "=" * 80)
    print("RECOMMENDATIONS:")
    print("1. Start the app to enable live sync checking")
    print("2. Login as admin and navigate to SharePoint import")
    print("3. Use the manual sync feature to update the index")
    print("4. The sync will show exact counts from SharePoint")
    print("=" * 80)

if __name__ == "__main__":
    analyze_sync_offline()